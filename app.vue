<template>
  <div class="bg-black/0 dark:bg-white/0">
    <NuxtLayout>
      <NuxtPage />      
    </NuxtLayout>
    <ParticlesBg
      class="fixed inset-0 -z-[999]"
      :quantity="100"
      :ease="100"
      :color="isDark ? '#ffffff' : '#000000'"
      :staticity="10"
      refresh
    />
  </div>
</template>

<script setup lang="ts">
const isDark = computed(() => useColorMode().value == "dark");
</script>

<style>
.page-enter-active,
.page-leave-active {
  transition: all 0.4s;
}
.page-enter-from,
.page-leave-to {
  opacity: 0;
  filter: blur(1rem);
}
</style>