{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@number-flow/vue": "^0.4.8", "@nuxt/image": "^1.10.0", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/i18n": "^9.5.6", "@pinia/nuxt": "^0.11.1", "@splinetool/runtime": "^1.10.16", "@tailwindcss/vite": "^4.1.10", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.5.0", "axios": "^1.10.0", "dotted-map": "^2.2.3", "echarts": "^5.6.0", "embla-carousel-vue": "^8.6.0", "gsap": "^3.13.0", "lucide-vue-next": "^0.523.0", "motion-v": "^1.5.0", "nuxt": "^3.17.5", "pinia": "^3.0.3", "postprocessing": "^6.37.6", "reka-ui": "^2.3.1", "shadcn-nuxt": "^2.2.0", "tailwindcss": "^4.1.10", "three": "^0.178.0", "vaul-vue": "^0.4.1", "vee-validate": "^4.15.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "vue-sonner": "^2.0.1", "zod": "^3.25.67"}, "devDependencies": {"@iconify-json/radix-icons": "^1.2.2", "@iconify/vue": "^5.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}}