<template>
  <div class="-mt-20">
    <div class="flex h-36 sm:h-50 w-full items-center justify-center">
      <!-- <InspiraLiquidLogo :image-url="imageUrl" class="animate-logo-entrance" /> -->
      <img
        src="/images/logo/logo1.png"
        alt="Crypto logo"
        class="max-w-full max-h-full object-contain"
      />
    </div>

    <header class="mb-12 text-center">
      <BlurReveal :delay="0.2" :duration="0.75" class="sm:p-4">
        <h1
          class="text-4xl md:text-6xl font-medium mb-4 text-black dark:text-white"
        >
          Crypto Gaming
        </h1>
      </BlurReveal>

         <p class="text-2xl text-black dark:text-white">
        เว็บเกม
        <ContainerTextFlip
          class="w-44 mx-2"
          :words="['หวยคริปโต', 'ลอตเตอรี่คริปโต', 'หวยคริปโต']"
        />
        ที่ดีที่สุดสำหรับคุณ
      </p>
    </header>

    <LampEffect />
    <Carousel
      class="mx-auto w-[230px] sm:w-auto justify-center items-center text-black dark:text-white"
    >
      <CarouselContent>
        <CarouselItem v-for="(game, index) in games" :key="index">
          <div class="flex justify-center items-center">
            <div
              @click="router.push(game.route)"
              class="game-card relative overflow-hidden rounded-2xl cursor-pointer transition-all h-[200px] sm:h-[300px] w-[250px] sm:w-[400px] duration-500 group"
              :style="{
                backgroundImage: `url(${game.imageUrl})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
              }"
            >
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent group-hover:backdrop-blur-[4px] transition-all duration-500"
              ></div>

              <GlowBorder
                :color="['#A07CFE', '#FE8FB5', '#FFBE7B']"
                :border-radius="15"
              />
              <div
                class="absolute inset-x-0 bottom-0 p-6 flex flex-col justify-center items-center gap-4 transform transition-all duration-500 group-hover:translate-y-0 group-hover:scale-105 z-10"
              >
                <InteractiveHoverButton :text="game.title" />
              </div>
            </div>
          </div>
        </CarouselItem>
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  </div>
</template>

<script lang="ts" setup>
const router = useRouter();
const imageUrl = "/images/logo/logo.png";

const games = [
  {
    title: "หวยคริปโต",
    route: "/crypto/crypto-rooms",
    imageUrl: "/images/crypto/crypto-bg.jpg",
  },
  // {
  //   title: "เกมลอตเตอรี่",
  //   route: "/lottery",
  //   imageUrl:
  //     "https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80",
  // },
];
</script>
