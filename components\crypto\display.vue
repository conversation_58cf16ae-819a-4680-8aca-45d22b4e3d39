<template>
  <div>
    <div class="fixed left-15 hidden sm:block">
      <div
        class="bg-gradient-to-r from-gray-100/80 to-gray-200/80 dark:from-gray-800/80 dark:to-gray-900/80 rounded-xl p-1.5 inline-flex shadow-lg border border-white/20 dark:border-blue-500/20 backdrop-blur-sm"
      >
        <div class="relative flex">
          <!-- Background Highlight for Selected Button -->
          <div
            class="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg transition-all duration-300 shadow-md"
            :style="{
              transform: `translateX(${timeframe === '1s' ? '0%' : '100%'})`,
              width: '50%',
            }"
          ></div>

          <button
            @click="setTimeframe('1s')"
            class="px-4 py-2 text-xs sm:text-sm rounded-lg transition-all duration-300 relative z-10 flex items-center gap-2 min-w-[90px] justify-center font-medium"
            :class="
              timeframe === '1s'
                ? 'text-white'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white'
            "
          >
            <Icon
              icon="lucide:clock"
              :class="timeframe === '1s' ? 'animate-pulse' : ''"
            />
            {{ $t("1s") }}
          </button>

          <button
            @click="setTimeframe('5s')"
            class="px-4 py-2 text-xs sm:text-sm rounded-lg transition-all duration-300 relative z-10 flex items-center gap-2 min-w-[90px] justify-center font-medium"
            :class="
              timeframe === '5s'
                ? 'text-white'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white'
            "
          >
            <Icon
              icon="lucide:timer"
              :class="timeframe === '5s' ? 'animate-pulse' : ''"
            />
            {{ $t("5s") }}
          </button>
        </div>
      </div>
    </div>

    <div
      class="mt-6 grid grid-cols-2 gap-1 xs:gap-3 sm:gap-4 md:gap-6 text-[10px] xs:text-xs sm:text-sm md:text-base max-w-7xl mx-auto"
    >
      <!-- Crypto Cards - Left Column -->
      <div
        class="h-[515px] bg-gradient-to-b from-gray-100/50 to-gray-300/50 dark:from-gray-800/50 dark:to-gray-900/50 rounded-xl p-2 sm:p-3 md:p-3 transition-all duration-300"
      >
        <h3
          class="text-center font-bold text-blue-600 dark:text-blue-400 mb-2 text-xs sm:text-base md:text-xl"
        >
          {{ $t("player") }}
        </h3>
        <Transition
          name="fade-scale"
          mode="out-in"
          enter-active-class="transition-all duration-700 ease-out"
          enter-from-class="opacity-0 transform scale-95"
          enter-to-class="opacity-100 transform scale-100"
          leave-active-class="transition-all duration-400 ease-in"
          leave-from-class="opacity-100 transform scale-100"
          leave-to-class="opacity-0 transform scale-105"
        >
          <CryptoResultPlayer
            v-if="timeoutBet"
            class="-mx-3 -mt-15"
            :data="data.coinPlayer" />

          <div v-else class="flex flex-col space-y-2 sm:space-y-3">
            <div
              v-for="(crypto, index) in playerCrypto"
              :key="crypto.name"
              class="bg-white/50 dark:bg-gray-800/50 border border-blue-500/20 dark:border-blue-500/20 rounded-xl p-2 sm:p-3 md:p-4 flex flex-col transform transition-all duration-200"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-1 sm:gap-2 md:gap-3">
                  <div
                    class="bg-gradient-to-r from-blue-500 to-purple-500 rounded-full p-1 flex items-center justify-center"
                  >
                    <img
                      :src="`/images/crypto/trading/${crypto.image}.png`"
                      :alt="crypto.name"
                      class="w-3 h-3 xs:w-4 xs:h-4 sm:w-5 sm:h-5 md:w-8 md:h-8 object-contain"
                    />
                  </div>
                  <span class="font-medium text-gray-800 dark:text-gray-200">{{
                    crypto.name
                  }}</span>
                </div>
                <div class="flex items-center gap-0.5 xs:gap-1 sm:gap-2">
                  <span class="font-bold text-gray-900 dark:text-white">
                    ${{ crypto.price.slice(0, -1)
                    }}<span
                      class="ml-0.5 sm:ml-1"
                      :class="`sm:text-3xl font-bold ${
                        crypto.change > 0
                          ? 'text-emerald-600 dark:text-emerald-400'
                          : crypto.change < 0
                          ? 'text-red-600 dark:text-red-400'
                          : ''
                      }`"
                      >{{ crypto.lastDigit }}</span
                    >
                  </span>
                  <span
                    :class="`${
                      crypto.change > 0
                        ? 'bg-emerald-500/10 text-emerald-600 dark:text-emerald-400'
                        : crypto.change < 0
                        ? 'bg-red-500/10 text-red-600 dark:text-red-400'
                        : 'bg-gray-500/10 text-gray-600 dark:text-gray-400'
                    } px-1 py-0.5 sm:px-2 sm:py-1 rounded-full flex items-center text-[8px] xs:text-[10px] sm:text-xs font-medium`"
                  >
                    <template v-if="crypto.change !== 0">
                      <Icon
                        :icon="`mdi:arrow-${crypto.change > 0 ? 'up' : 'down'}`"
                        class="mr-0.5"
                      />
                    </template>
                  </span>
                </div>
              </div>
              <div :id="`chart-left-${index}`" class="w-full h-3 sm:h-35"></div>
            </div></div
        ></Transition>
      </div>

      <!-- Crypto Cards - Right Column -->
      <div
        class="bg-gradient-to-b h-[515px] from-gray-100/50 to-gray-300/50 dark:from-gray-800/50 dark:to-gray-900/50 rounded-xl p-2 sm:p-3 md:p-3 transition-all duration-300"
      >
        <h3
          class="text-center font-bold text-red-600 dark:text-red-400 mb-2 text-xs sm:text-base md:text-xl"
        >
          {{ $t("banker") }}
        </h3>
        <Transition
          name="fade-scale"
          mode="out-in"
          enter-active-class="transition-all duration-700 ease-out"
          enter-from-class="opacity-0 transform scale-95"
          enter-to-class="opacity-100 transform scale-100"
          leave-active-class="transition-all duration-400 ease-in"
          leave-from-class="opacity-100 transform scale-100"
          leave-to-class="opacity-0 transform scale-105"
        >
          <CryptoResultBanker
            v-if="timeoutBet"
            class="-mx-3 -mt-15"
            :data="data.coinBanker"
          />
          <div v-else class="flex flex-col space-y-2 sm:space-y-3">
            <div
              v-for="(crypto, index) in bankerCrypto"
              :key="crypto.name"
              class="bg-white/50 dark:bg-gray-800/50 border border-red-500/20 dark:border-red-500/20 rounded-xl p-2 sm:p-3 md:p-4 flex flex-col transform transition-all duration-200"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-1 sm:gap-2 md:gap-3">
                  <div
                    class="bg-gradient-to-r from-purple-500 to-blue-500 rounded-full p-1 flex items-center justify-center"
                  >
                    <img
                      :src="`/images/crypto/trading/${crypto.image}.png`"
                      :alt="crypto.name"
                      class="w-3 h-3 xs:w-4 xs:h-4 sm:w-5 sm:h-5 md:w-8 md:h-8 object-contain"
                    />
                  </div>
                  <span class="font-medium text-gray-800 dark:text-gray-200">{{
                    crypto.name
                  }}</span>
                </div>
                <div class="flex items-center gap-0.5 xs:gap-1 sm:gap-2">
                  <span class="font-bold text-gray-900 dark:text-white">
                    ${{ crypto?.price.slice(0, -1)
                    }}<span
                      class="ml-0.5 sm:ml-1"
                      :class="`sm:text-3xl font-bold ${
                        crypto?.change > 0
                          ? 'text-emerald-600 dark:text-emerald-400'
                          : crypto.change < 0
                          ? 'text-red-600 dark:text-red-400'
                          : ''
                      }`"
                      >{{ crypto?.lastDigit }}</span
                    >
                  </span>
                  <span
                    :class="`${
                      crypto?.change > 0
                        ? 'bg-emerald-500/10 text-emerald-600 dark:text-emerald-400'
                        : crypto?.change < 0
                        ? 'bg-red-500/10 text-red-600 dark:text-red-400'
                        : 'bg-gray-500/10 text-gray-600 dark:text-gray-400'
                    } px-1 py-0.5 sm:px-2 sm:py-1 rounded-full flex items-center text-[8px] xs:text-[10px] sm:text-xs font-medium`"
                  >
                    <template v-if="crypto?.change !== 0">
                      <Icon
                        :icon="`mdi:arrow-${
                          crypto?.change > 0 ? 'up' : 'down'
                        }`"
                        class="mr-0.5"
                      />
                    </template>
                  </span>
                </div>
              </div>
              <div
                :id="`chart-right-${index}`"
                class="w-auto h-3 sm:h-35"
              ></div>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps(["timeoutBet", "data", "pattern"]);
import * as echarts from "echarts/core";
import { LineChart, CandlestickChart } from "echarts/charts";
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  DataZoomComponent,
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";

echarts.use([
  LineChart,
  CandlestickChart,
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  DataZoomComponent,
  CanvasRenderer,
]);
const isLoading = ref(false);

const timeframe = ref("1s");

const aggregateTo5sData = (data1s) => {
  const data5s = [];

  if (data1s.length < 5) {
    return data1s;
  }

  for (let i = 0; i < data1s.length; i += 5) {
    const chunk = data1s.slice(i, i + 5);
    if (chunk.length < 5) {
      if (chunk.length > 1) {
        const open = chunk[0][0];
        const close = chunk[chunk.length - 1][1];
        const low = Math.min(...chunk.map((c) => c[2]));
        const high = Math.max(...chunk.map((c) => c[3]));
        data5s.push([open, close, low, high]);
      }
      break;
    }

    const open = chunk[0][0];
    const close = chunk[chunk.length - 1][1];
    const low = Math.min(...chunk.map((c) => c[2]));
    const high = Math.max(...chunk.map((c) => c[3]));

    data5s.push([open, close, low, high]);
  }

  return data5s.length > 0 ? data5s : data1s;
};

const playerCrypto = computed(() => {
  if (!props.pattern?.playerCrypto) return [];

  return props.pattern.playerCrypto.map((crypto) => {
    const latestData = crypto.patternData[crypto.patternData.length - 1];
    const previousData = crypto.patternData[crypto.patternData.length - 2];

    const currentPrice = latestData[1]; // close
    const previousPrice = previousData ? previousData[1] : latestData[0];
    const change = ((currentPrice - previousPrice) / previousPrice) * 100;

    const priceString = currentPrice.toString();
    const lastDigit = priceString.charAt(priceString.length - 1);
    const priceWithoutLastDigit = priceString.slice(0, -1);

    return {
      ...crypto,
      price: priceString,
      priceWithoutLastDigit,
      lastDigit,
      change: parseFloat(change.toFixed(2)),
    };
  });
});

const bankerCrypto = computed(() => {
  if (!props.pattern?.bankerCrypto) return [];

  return props.pattern.bankerCrypto.map((crypto) => {
    const latestData = crypto.patternData[crypto.patternData.length - 1];
    const previousData = crypto.patternData[crypto.patternData.length - 2];

    const currentPrice = latestData[1];
    const previousPrice = previousData ? previousData[1] : latestData[0];
    const change = ((currentPrice - previousPrice) / previousPrice) * 100;

    const priceString = currentPrice.toString();
    const priceWithoutLastDigit = priceString.slice(0, -1);
    const lastDigit = priceString.charAt(priceString.length - 1);

    return {
      ...crypto,
      price: priceString,
      priceWithoutLastDigit,
      lastDigit,
      change: parseFloat(change.toFixed(2)),
    };
  });
});

const setTimeframe = async (newTimeframe) => {
  isLoading.value = true;
  timeframe.value = newTimeframe;

  await new Promise((resolve) => setTimeout(resolve, 300));

  renderAllCharts();
  isLoading.value = false;
};

const renderCandlestickChart = (elementId, data) => {
  const chartDom = document.getElementById(elementId);
  if (!chartDom) return;
  const existingChart = echarts.getInstanceByDom(chartDom);

  if (existingChart) {
    existingChart.setOption({
      series: [
        {
          data: data,
        },
      ],
    });
  } else {
    const myChart = echarts.init(chartDom);

    const option = {
      tooltip: {
        trigger: "none",
        axisPointer: {
          type: "cross",
        },
      },
      grid: {
        left: "3%",
        right: "3%",
        bottom: "10%",
        top: "5%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        show: true,
        axisLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.05)",
          },
        },
        axisLabel: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        scale: true,
        show: true,
        axisLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "rgba(255, 255, 255, 0.05)",
            type: "line",
          },
        },
      },
      series: [
        {
          type: "candlestick",
          data: data,
          itemStyle: {
            color: "#10b981",
            color0: "#ef4444",
            borderColor: "#10b981",
            borderColor0: "#ef4444",
          },
          animationDuration: 1000,
          animationEasing: "cubicOut",
        },
      ],
      animation: true,
    };

    myChart.setOption(option);
    setTimeout(() => {
      myChart.resize();
    }, 0);
  }
};

const renderAllCharts = () => {
  playerCrypto.value.forEach((crypto, index) => {
    let data;
    switch (timeframe.value) {
      case "1s":
        data = crypto.patternData;
        break;
      case "5s":
        data = aggregateTo5sData(crypto.patternData);
        break;
    }
    renderCandlestickChart(`chart-left-${index}`, data);
  });

  bankerCrypto.value.forEach((crypto, index) => {
    let data;
    switch (timeframe.value) {
      case "1s":
        data = crypto.patternData;
        break;
      case "5s":
        data = aggregateTo5sData(crypto.patternData);
        break;
    }
    renderCandlestickChart(`chart-right-${index}`, data);
  });
};

onMounted(() => {
  renderAllCharts();

  window.addEventListener("resize", () => {
    renderAllCharts();
  });
});

onUnmounted(() => {
  window.removeEventListener("resize", renderAllCharts);
});
</script>

<style scoped>
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
