<script setup lang="ts">
const props = defineProps({
  initialSeconds: {
    type: Number,
    default: 20,
  },
  onComplete: {
    type: Function,
    default: () => {},
  },
  autoStart: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(["timeUpdate", "complete"]);

const timeLeft = ref(props.initialSeconds);
const isRunning = ref(props.autoStart);
const timerInterval = ref(null);
const isCompleted = ref(false);
const smoothPercentage = ref(100); // Add this for smooth animation

const percentage = computed(() => {
  return (timeLeft.value / props.initialSeconds) * 100;
});

const formattedTime = computed(() => {
  if (isCompleted.value) return "0";
  return timeLeft.value.toString().padStart(1, "0");
});

const colorClass = computed(() => {
  if (isCompleted.value) {
    return "from-green-500 to-teal-500";
  } else if (timeLeft.value > 10) {
    return "from-blue-500 to-purple-500";
  } else if (timeLeft.value > 5) {
    return "from-yellow-500 to-orange-500";
  } else {
    return "from-red-500 to-pink-500";
  }
});

const pulseClass = computed(() => {
  if (isCompleted.value) return "animate-pulse";
  return timeLeft.value <= 5 ? "animate-pulse" : "";
});

// Add animation frame handler for smooth animation
let animationFrameId = null;
const lastUpdateTime = ref(Date.now());

const updateSmoothPercentage = () => {
  if (!isRunning.value) return;
  
  const now = Date.now();
  const deltaTime = now - lastUpdateTime.value;
  lastUpdateTime.value = now;
  
  // Calculate how much percentage to decrease per millisecond
  const decreasePerMs = 100 / (props.initialSeconds * 1000);
  const newPercentage = smoothPercentage.value - (deltaTime * decreasePerMs);
  
  if (newPercentage <= 0) {
    smoothPercentage.value = 0;
    return;
  }
  
  smoothPercentage.value = newPercentage;
  animationFrameId = requestAnimationFrame(updateSmoothPercentage);
};

const startTimer = () => {
  if (isRunning.value) return;

  isCompleted.value = false;
  isRunning.value = true;
  lastUpdateTime.value = Date.now();
  smoothPercentage.value = 100;
  
  // Start smooth animation
  animationFrameId = requestAnimationFrame(updateSmoothPercentage);
  
  timerInterval.value = setInterval(() => {
    if (timeLeft.value > 0) {
      timeLeft.value--;
      emit("timeUpdate", timeLeft.value);
    } else {
      stopTimer();
      isCompleted.value = true;
      emit("complete");
      props.onComplete();
    }
  }, 1000);
};

const stopTimer = () => {
  isRunning.value = false;
  clearInterval(timerInterval.value);
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
};

const resetTimer = () => {
  stopTimer();
  isCompleted.value = false;
  timeLeft.value = props.initialSeconds;
  smoothPercentage.value = 100;
};

onMounted(() => {
  if (props.autoStart) {
    startTimer();
  }
});

onUnmounted(() => {
  stopTimer();
});

defineExpose({
  startTimer,
  stopTimer,
  resetTimer,
  timeLeft,
  isCompleted,
});
</script>

<template>
  <div class="w-[150px] z-3">
    <!-- Main timer container with glass effect -->
    <div
      class="relative aspect-square rounded-full overflow-hidden backdrop-blur-xl shadow-lg flex items-center justify-center"
    >
      <!-- Background gradient -->
      <div class="absolute inset-0 opacity-80"></div>

      <!-- Circular progress indicator -->
      <div class="absolute inset-0">
        <svg class="w-full h-full" viewBox="0 0 100 100">
          <!-- Background circle -->
          <circle
            cx="50"
            cy="50"
            r="45"
            fill="none"
            stroke="currentColor"
            stroke-width="4"
            class="text-gray-700/30"
          />
          <!-- Progress circle -->
          <circle
            cx="50"
            cy="50"
            r="45"
            fill="none"
            stroke="currentColor"
            stroke-width="4"
            stroke-linecap="round"
            :stroke-dasharray="`${2 * Math.PI * 45}`"
            :stroke-dashoffset="`${2 * Math.PI * 45 * (1 - smoothPercentage / 100)}`"
            transform="rotate(-90 50 50)"
            :class="`${
              isCompleted
                ? 'text-emerald-500 '
                : timeLeft > 10
                ? 'text-gray-400 dark:text-white '
                : timeLeft > 5
                ? 'text-amber-400 '
                : 'text-rose-500 '
            }`"
          />
        </svg>
      </div>

      <!-- Inner content -->
      <div class="relative z-10 flex flex-col items-center justify-center">
        <!-- Time display -->
        <div
          :class="`text-center font-bold bg-gradient-to-r ${colorClass} bg-clip-text text-transparent ${pulseClass}`"
        >
          <template v-if="isCompleted"
            ><span class="text-2xl flex items-center">{{ $t('close-bet') }}</span><span>{{ $t('wait-result') }}</span></template
          >
          <template v-else
            ><span class="text-6xl flex items-center">{{
              formattedTime
            }}</span></template
          >
        </div>
      </div>

      <!-- Glowing effect -->
      <div
        :class="`absolute inset-0 rounded-full blur-md opacity-20 bg-gradient-to-r ${colorClass}`"
      ></div>
    </div>
  </div>
</template>

<style scoped></style>