<template>
  <div>
    <BlurReveal :delay="0.2" :duration="0.75">
      <h1
        class="text-3xl sm:text-5xl mb-10 sm:mb-20 text-black dark:text-white font-medium text-center"
      >
        ธนาคารที่รองรับ ❤️
      </h1>
    </BlurReveal>
    <div
      ref="containerRef"
      class="relative flex h-[350px] sm:h-[700px] w-full items-center justify-center overflow-hidden"
    >
      <div
        class="flex size-full max-w-4xl flex-wrap items-center justify-center gap-8 text-2xl"
      >
        <!-- Center logo -->
        <div
          ref="centerRef"
          class="z-20 flex size-20 sm:size-32 items-center justify-center rounded-full border-2 bg-white p-2 text-black shadow-[0_0_30px_-12px_rgba(0,0,0,0.8)] absolute"
        >
          <!-- <InspiraLiquidLogo :image-url="imageUrl" /> -->
           <img
            src="/images/crypto/logocrypto.png"
            alt="Crypto logo"
            class="max-w-full max-h-full object-contain"
          />
        </div>

        <!-- Bank icons arranged in a circle around the center -->
        <div
          v-for="(bank, index) in banks"
          :key="index"
          :ref="(el) => (bankRefs[index] = el)"
          class="z-10 flex size-10 sm:size-16 items-center justify-center rounded-full border-2 bg-white p-2 shadow-[0_0_20px_-12px_rgba(0,0,0,0.8)] dark:text-black"
          :style="getBankPosition(index)"
        >
          <img
            :src="`/images/banks/${bank.icon}`"
            alt="Bank icon"
            class="max-w-full max-h-full object-contain"
          />
        </div>
      </div>

      <!-- Animated beams connecting each bank to the center -->
      <AnimatedBeam
        v-for="(_, index) in banks"
        :key="index"
        :container-ref="containerRef"
        :from-ref="bankRefs[index]"
        :to-ref="centerRef"
        :curvature="getCurvature(index)"
        :end-y-offset="getYOffset(index)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const containerRef = ref(null);
const centerRef = ref(null);
const bankRefs = reactive(Array(23).fill(null));
const imageUrl = "/images/logo/logo.png";
const isMobile = ref(false);

// List of 25 bank icons
const banks = [
  { icon: "baac.webp" },
  { icon: "bay.webp" },
  { icon: "bbl.webp" },
  { icon: "cimb.webp" },
  { icon: "citi.webp" },
  { icon: "ghb.webp" },
  { icon: "gsb.webp" },
  { icon: "kbank.webp" },
  { icon: "icbc.webp" },
  { icon: "isbt.webp" },
  { icon: "jpm.webp" },
  { icon: "kbank.webp" },
  { icon: "kkp.webp" },
  { icon: "ktb.webp" },
  { icon: "lh.webp" },
  { icon: "rbs.webp" },
  { icon: "scb.webp" },
  { icon: "stan.webp" },
  { icon: "tisco.webp" },
  { icon: "true.webp" },
  { icon: "truemoney.webp" },
  { icon: "ttb.webp" },
  { icon: "uob.webp" },
];

// Check if device is mobile
const checkMobile = () => {
  isMobile.value = window.innerWidth < 640;
};

onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});

// Calculate position for each bank in a circle around the center
const getBankPosition = (index) => {
  const totalBanks = banks.length;
  // Adjust radius based on screen size
  const baseRadius = isMobile.value ? 150 : 280;
  
  // Add slight variation to radius for more natural look
  const radiusVariation = isMobile.value ? 10 : 20;
  const adjustedRadius =
    baseRadius + (Math.random() * radiusVariation - radiusVariation / 2);

  // Distribute evenly but with slight randomness
  const baseAngle = (index / totalBanks) * 2 * Math.PI;
  const angleVariation = 0.05; // Small angle variation
  const angle =
    baseAngle + (Math.random() * angleVariation - angleVariation / 2);

  const x = Math.cos(angle) * adjustedRadius;
  const y = Math.sin(angle) * adjustedRadius;

  return {
    transform: `translate(${x}px, ${y}px)`,
    position: "absolute",
  };
};

// Calculate appropriate curvature based on position
const getCurvature = (index) => {
  const angle = (index / banks.length) * 360;
  // Vary curvature based on position
  if (angle < 90 || angle > 270) {
    return -50 - Math.random() * 30;
  } else {
    return 50 + Math.random() * 30;
  }
};

// Calculate y-offset based on position
const getYOffset = (index) => {
  const angle = (index / banks.length) * 360;
  if (angle < 180) {
    return -5 - Math.random() * 10;
  } else {
    return 5 + Math.random() * 10;
  }
};
</script>