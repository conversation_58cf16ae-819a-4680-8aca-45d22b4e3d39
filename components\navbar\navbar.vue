<template>
  <nav
    class="fixed w-full text-black dark:text-white py-4 px-2 border-b sm:ml-[0.5px] backdrop-blur-xl z-[15] bg-white dark:bg-black/50 sm:dark:bg-black/10"
  >
    <div class="px-2 flex justify-between items-center">
      <NuxtLink
        to="/"
        class="flex items-center gap-3 hover:opacity-80 transition-opacity"
      >
        <div class="relative h-8 sm:h-10">
          <!-- <InspiraLiquidLogo :image-url="imageUrl" class="h-full w-full" /> -->
          <img
            src="/images/logo/logo1.png"
            alt="Crypto logo"
            class="max-w-full max-h-full object-contain"
          />
        </div>
        <div class="flex flex-col">
          <h1
            class="text-sm sm:text-xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent"
          >
           {{ $t('crypto-gaming') }}
          </h1>
          <RadiantText
            class="text-xs transition ease-out hover:text-neutral-600 hover:duration-300 hover:dark:text-neutral-400"
            :duration="5"
          >
            {{ $t('crypto1') }}</RadiantText
          >
        </div>
      </NuxtLink>

      <nav class="flex items-center gap-5">
        <CryptoRules class="hidden sm:flex" />
        <div class="flex items-center gap-1 sm:gap-4">
          <!-- <div
            class="bg-gradient-to-r from-blue-500 to-purple-600 px-2 py-1 rounded-full flex items-center gap-1"
          >
            <span class="text-white text-xs sm:text-[16px] flex items-center">
              <Icon icon="mdi:currency-btc" />
              <NumberTicker :value="balance" class="text-white" />
            </span>
            <button
              @click="refreshBalance"
              class="bg-white/90 rounded-full p-0.5 sm:p-1 hover:bg-white transition-colors cursor-pointer"
              title="รีเฟรชยอดเงิน"
            >
              <Icon
                icon="mdi:refresh"
                class="text-black dark:text-black text-xs"
                :class="loading ? 'animate-spin' : ''"
              />
            </button>
          </div> -->

          <ShadcnSwicthMode class="hidden sm:flex" />
          <ShadcnFullscreen />
          <ShadcnMenuMobile class="flex sm:hidden" />
          <CryptoSound class="hidden sm:flex" />
          <CryptoHistory class="hidden sm:flex" />
          <ShadcnLanguage class="hidden sm:flex" />
          <CryptoProfile class="hidden sm:flex" />
        </div>
      </nav>
    </div>
  </nav>
</template>

<script setup>
const balance = ref(99999);
const loading = ref(false);
const imageUrl = "/images/logo/logo.png";
const refreshBalance = () => {
  loading.value = true;

  setTimeout(() => {
    balance.value = Math.floor(Math.random() * 50000) + 5000;
    loading.value = false;
  }, 700);
};

const Commas = (value) => {
  return `${value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
};
</script>
