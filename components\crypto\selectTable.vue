<script setup lang="ts">
const props = defineProps(["data"]);
const formattedPattern = computed(() => {
  if (!props.data || !props.data?.pattern) return [];

  const pattern = props.data.pattern;
  const grid = [];
  let currentCol = [];
  let lastResult = null;

  pattern.forEach((result) => {
    if (
      (result !== lastResult && lastResult !== null) ||
      currentCol.length >= 6
    ) {
      grid.push([...currentCol]);
      currentCol = [result];
    } else {
      currentCol.push(result);
    }
    lastResult = result;
  });

  if (currentCol.length > 0) {
    grid.push([...currentCol]);
  }

  return grid;
});

// สร้าง empty columns ไว้ล่วงหน้าเพื่อลดการคำนวณซ้ำ
const emptyColumns = computed(() => {
  const count = Math.min(12, Math.max(0, 13 - formattedPattern.value.length));
  return Array.from({ length: count }, (_, i) => i);
});

// สร้าง empty rows ไว้ล่วงหน้า
const emptyRows = Array.from({ length: 6 }, (_, i) => i);

const scrollContainer = ref(null);

const scrollToLatest = () => {
  if (scrollContainer.value) {
    scrollContainer.value.scrollLeft = scrollContainer.value.scrollWidth;
  }
};

watch(
  () => props.data?.pattern,
  () => {
    nextTick(() => {
      scrollToLatest();
    });
  },
  { deep: true }
);

onMounted(() => {
  scrollToLatest();
});
</script>

<template>
  <div
    class=" border-white/30 dark:border-gray-700/20 flex items-center justify-center"
  >
    <!-- Pattern Grid -->
    <div>
      <div ref="scrollContainer" class="overflow-auto w-[205px]">
        <div class="inline-flex gap-1">
          <div v-for="(column, colIndex) in formattedPattern" :key="colIndex">
            <div class="flex flex-col gap-2">
              <div v-for="(result, rowIndex) in column" :key="rowIndex">
                <div class="relative group">
                  <div
                    class="aspect-square h-3 w-3 rounded-xs flex justify-center items-center relative"
                  >
                    <div class="absolute inset-0 rounded-lg overflow-hidden">
                      <div
                        class="absolute inset-0 opacity-20"
                        :class="{
                          'bg-[#fa2956]': result === 'B',
                          'bg-[#0066FF]': result === 'P',
                          'bg-[#4CAF50]': result === 'T',
                        }"
                      ></div>
                    </div>

                    <div
                      class="h-3 w-3 text-[12px] rounded-xs flex justify-center items-center text-white font-bold shadow-lg"
                      :class="{
                        'border-[#fa2956] border-2': result === 'B',
                        'border-[#0066FF] border-2': result === 'P',
                        'border-[#4CAF50] border-2': result === 'T',
                      }"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- ใช้ emptyRows ที่สร้างไว้ล่วงหน้า -->
              <div
                v-for="emptyIndex in emptyRows.slice(
                  0,
                  Math.max(0, 6 - column.length)
                )"
                :key="`empty-${emptyIndex}`"
              >
                <div
                  class="aspect-square h-3 w-3 rounded-xs flex justify-center items-center bg-white/40 border border-gray-800/30"
                ></div>
              </div>
            </div>
          </div>

          <!-- ใช้ emptyColumns ที่คำนวณไว้ล่วงหน้า -->
          <div v-for="emptyCol in emptyColumns" :key="`empty-col-${emptyCol}`">
            <div class="flex flex-col gap-2">
              <div
                v-for="emptyRow in emptyRows"
                :key="`empty-cell-${emptyRow}`"
              >
                <div
                  class="aspect-square h-3 w-3 rounded-xs flex justify-center items-center bg-white/40 border border-gray-800/30"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.overflow-auto::-webkit-scrollbar {
  height: 1px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.2);
  border-radius: 10px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 10px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
