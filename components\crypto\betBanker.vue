<template>
  <div
    class="bg-gradient-to-b from-gray-100/50 to-gray-300/50 dark:from-gray-800/50 dark:to-gray-900/50 border rounded-lg p-2 sm:p-4 backdrop-blur-sm"
  >
    <!-- Banker Header -->
    <div
      class="flex items-center justify-between mb-2 pb-2 border-b border-gray-200 dark:border-gray-700"
    >
      <div class="flex items-center gap-1.5">
        <div class="w-2 h-2 rounded-full bg-red-500"></div>
        <h3 class="text-sm font-medium text-red-500">{{ $t("banker") }}</h3>
      </div>
      <div class="text-xs text-gray-500">{{ $t("select-bet") }}</div>
    </div>

    <!-- Betting options for Banker -->
    <div class="grid grid-cols-2 gap-1.5 sm:gap-2">
      <button
        v-for="(option, key) in betOptions"
        :key="key"
        @click="$emit('place-bet', 'banker', key)"
        class="group relative overflow-hidden cursor-pointer p-1.5 sm:p-3 rounded-lg text-center text-xs sm:text-base transition-all duration-200 border-2"
        :class="[
          (betConfirmed || timeoutBet) && bets[key] === 0
            ? 'bg-gray-600/10 hover:bg-gray-600/20 border-gray-600/30 text-gray-500 '
            : 'bg-white dark:bg-gray-800 hover:bg-red-100 border-red-300 dark:border-red-700',
        ]"
      >
        <div class="flex flex-col items-center justify-center">
          <Icon
            :icon="option.icon"
            class="mb-1 transition-transform hidden sm:block"
            :class="[
              (betConfirmed || timeoutBet) && bets[key] === 0
                ? 'text-gray-500'
                : 'text-red-500',
            ]"
          />
          <span
            class="font-medium"
            :class="[(betConfirmed || timeoutBet) && bets[key] === 0 ? 'text-gray-500' : '']"
            >{{ option.label }}</span
          >
          <span
            class="text-xs"
            :class="[
              (betConfirmed || timeoutBet) && bets[key] === 0
                ? 'text-gray-500'
                : 'text-gray-500',
            ]"
            >{{ option.odds }}</span
          >
        </div>
        <div
          v-if="bets[key] > 0"
          class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-red-500/10 rounded-full w-9 sm:w-15 h-9 sm:h-15 flex items-center justify-center shadow-lg border-2 border-white/30 dark:border-white/20 overflow-hidden backdrop-blur-lg text-black dark:text-white"
          style="box-shadow: 0 0 10px rgba(239, 68, 68, 0.5)"
        >
          <div
            class="absolute inset-0 rounded-full border-4 border-red-400 border-dashed animate-spin-slow"
          ></div>
          <div
            class="flex items-center justify-center sm:text-xl font-bold text-red-500 dark:text-white"
          >
            <!-- <NumberTicker :value="bets[key]" :decimalPlaces="0" />  -->
            {{ formatChipValue(bets[key]) }}
          </div>
        </div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n();
const formatChipValue = (value) => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    const kValue = value / 1000;
    return kValue % 1 === 0 ? `${kValue}K` : `${kValue.toFixed(1)}K`;
  }
  return value.toString();
};
defineProps({
  bets: {
    type: Object,
    required: true,
    default: () => ({
      high: 0,
      low: 0,
      odd: 0,
      even: 0,
    }),
  },
  betConfirmed: {
    type: Boolean,
    default: false,
  },
  timeoutBet: {
    type: Boolean,
    default: false,
  },
});

defineEmits(["place-bet"]);

const betOptions = computed(() => {
  return {
    high: {
      icon: "lucide:arrow-up",
      label: t("high"),
      odds: "x1.98",
    },
    low: {
      icon: "lucide:arrow-down",
      label: t("low"),
      odds: "x1.98",
    },
    odd: {
      icon: "lucide:dice-1",
      label: t("odd"),
      odds: "x1.98",
    },
    even: {
      icon: "lucide:dice-2",
      label: t("even"),
      odds: "x1.98",
    },
  };
});
</script>

<style scoped>
.animate-spin-slow {
  animation: spin 8s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
