<template>
  <!-- PLAYER Summary Statistics -->
  <div
    class="fixed left-5 bottom-80 z-8 w-[570px] bg-blue-500/10 rounded-lg p-2 border border-blue-500/20 hidden sm:block"
  >
    <div class="flex flex-col gap-2" v-if="playerStats">
      <div
        v-if="playerSummaryTab === 'highlow'"
        class="flex flex-col gap-1 sm:gap-2"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-1 sm:gap-2">
            <button
              @click="playerSummaryTab = 'highlow'"
              class="text-[8px] xs:text-[10px] sm:text-xs md:text-sm px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-md font-medium transition-all duration-200 cursor-pointer"
              :class="
                playerSummaryTab === 'highlow'
                  ? 'bg-blue-500 text-white'
                  : 'bg-blue-500/20 text-blue-500 hover:bg-blue-500/30'
              "
            >
              {{ $t("high-low") }}
            </button>
            <button
              @click="playerSummaryTab = 'oddeven'"
              class="text-[8px] xs:text-[10px] sm:text-xs md:text-sm px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-md font-medium transition-all duration-200 cursor-pointer"
              :class="
                playerSummaryTab === 'oddeven'
                  ? 'bg-blue-500 text-white'
                  : 'bg-blue-500/20 text-blue-500 hover:bg-blue-500/30'
              "
            >
              {{ $t("even-odd") }}
            </button>
          </div>
          <div class="flex items-center gap-1.5 sm:gap-2">
            <span
              class="text-[8px] xs:text-[10px] sm:text-xs md:text-sm font-medium"
            >
              <span class="text-red-500">
                {{ $t("high") }} {{ playerStats.highPercent }}%</span
              >
              /
              <span class="text-blue-500">
                {{ $t("low") }} {{ playerStats.lowPercent }}%</span
              >
            </span>
          </div>
        </div>

        <div
          class="w-full h-2 sm:h-3 md:h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden"
        >
          <div class="flex h-full">
            <div
              class="bg-gradient-to-r from-red-600 to-red-400 h-full"
              :style="`width: ${playerStats.highPercent}%`"
            ></div>
            <div
              class="bg-gradient-to-r from-blue-400 to-blue-600 h-full"
              :style="`width: ${playerStats.lowPercent}%`"
            ></div>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <span
            class="text-[8px] xs:text-[10px] sm:text-xs md:text-sm text-gray-500"
          >
            {{ $t("10last-result") }}</span
          >
          <div class="flex items-center gap-0.5 sm:gap-1">
            <div
              v-for="(result, index) in playerStats.last10HighLow"
              :key="`player-summary-${index}`"
              :class="`${
                result === 'high' ? 'bg-red-500' : 'bg-blue-500'
              } w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold`"
            >
              {{ props.data?.resultPlayer?.slice(-10)[index] }}
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="playerSummaryTab === 'oddeven'"
        class="flex flex-col gap-1 sm:gap-2"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-1 sm:gap-2">
            <button
              @click="playerSummaryTab = 'highlow'"
              class="text-[8px] xs:text-[10px] sm:text-xs md:text-sm px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-md font-medium transition-all duration-200 cursor-pointer"
              :class="
                playerSummaryTab === 'highlow'
                  ? 'bg-blue-500 text-white'
                  : 'bg-blue-500/20 text-blue-500 hover:bg-blue-500/30'
              "
            >
              {{ $t("high-low") }}
            </button>
            <button
              @click="playerSummaryTab = 'oddeven'"
              class="text-[8px] xs:text-[10px] sm:text-xs md:text-sm px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-md font-medium transition-all duration-200 cursor-pointer"
              :class="
                playerSummaryTab === 'oddeven'
                  ? 'bg-blue-500 text-white'
                  : 'bg-blue-500/20 text-blue-500 hover:bg-blue-500/30'
              "
            >
              {{ $t("even-odd") }}
            </button>
          </div>
          <div class="flex items-center gap-1.5 sm:gap-2">
            <span
              class="text-[8px] xs:text-[10px] sm:text-xs md:text-sm font-medium"
            >
              <span class="text-purple-500">
                {{ $t("even") }} {{ playerStats.evenPercent }}%</span
              >
              /
              <span class="text-green-500">
                {{ $t("odd") }} {{ playerStats.oddPercent }}%</span
              >
            </span>
          </div>
        </div>

        <div
          class="w-full h-2 sm:h-3 md:h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden"
        >
          <div class="flex h-full">
            <div
              class="bg-gradient-to-r from-purple-600 to-purple-400 h-full"
              :style="`width: ${playerStats.evenPercent}%`"
            ></div>
            <div
              class="bg-gradient-to-r from-green-400 to-green-600 h-full"
              :style="`width: ${playerStats.oddPercent}%`"
            ></div>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <span
            class="text-[8px] xs:text-[10px] sm:text-xs md:text-sm text-gray-500"
          >
            {{ $t("10last-result") }}</span
          >
          <div class="flex items-center gap-0.5 sm:gap-1">
            <div
              v-for="(result, index) in playerStats.last10OddEven"
              :key="`player-summary-odd-${index}`"
              :class="`${
                result === 'even' ? 'bg-purple-500' : 'bg-green-500'
              } w-6 h-6  rounded-full flex items-center justify-center text-white text-xs font-bold`"
            >
              {{ props.data?.resultPlayer?.slice(-10)[index] }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps(["data"]);
const playerSummaryTab = ref("highlow");

const calculateResults = (results) => {
  if (!results || !Array.isArray(results)) return null;

  const stats = {
    high: 0,
    low: 0,
    even: 0,
    odd: 0,
    total: results.length,
    last10HighLow: [],
    last10OddEven: [],
  };

  results.forEach((num) => {
    // คำนวณ สูง/ต่ำ (0-13 = ต่ำ, 14-27 = สูง)
    if (num >= 14) {
      stats.high++;
      stats.last10HighLow.push("high");
    } else {
      stats.low++;
      stats.last10HighLow.push("low");
    }

    // คำนวณ คู่/คี่
    if (num % 2 === 0) {
      stats.even++;
      stats.last10OddEven.push("even");
    } else {
      stats.odd++;
      stats.last10OddEven.push("odd");
    }
  });

  // เอาแค่ 10 ตัวล่าสุด
  stats.last10HighLow = stats.last10HighLow.slice(-10);
  stats.last10OddEven = stats.last10OddEven.slice(-10);

  // คำนวณเปอร์เซ็นต์
  stats.highPercent = Math.round((stats.high / stats.total) * 100);
  stats.lowPercent = Math.round((stats.low / stats.total) * 100);
  stats.evenPercent = Math.round((stats.even / stats.total) * 100);
  stats.oddPercent = Math.round((stats.odd / stats.total) * 100);

  return stats;
};

const playerStats = computed(() => {
  return calculateResults(props.data?.resultPlayer);
});
</script>
