const rooms = ref([
  {
    id: 1,
    name: "Room 1",
    coinPlayer: [
      {
        name: "Bitcoin",
        image: "btc",
        chartData: [42500.25, 42510.75, 42490.5, 42515.3],
      },
      {
        name: "Ether<PERSON>",
        image: "eth",
        chartData: [42500.25, 42510.75, 42490.5, 42515.8],
      },
    ],
    coinBanker: [
      {
        name: "Binance",
        image: "bnb",
        chartData: [42500.25, 42510.75, 42490.5, 42200.7],
      },
      {
        name: "X<PERSON>",
        image: "xrp",
        chartData: [42500.25, 42510.75, 42490.5, 42515.9],
      },
    ],
    resultPlayer: [18, 12, 15, 17, 6, 9, 11, 5, 3, 0, 4, 12, 15, 17, 7, 2],
    resultBanker: [14, 16, 13, 11, 10, 5, 12, 15, 17, 6, 9, 11, 5, 3, 0, 4],

    playersAll: 124,
    time: false,

    player: 9,
    banker: 10,
    tie: 6,
    pattern: [
      "B",
      "B",
      "B",
      "B",
      "P",
      "T",
      "T",
      "P",
      "T",
      "B",
      "P",
      "P",
      "P",
      "P",
      "P",
      "P",
      "P",
      "T",
      "B",
      "B",
      "B",
      "B",
      "B",
      "T",
      "T",
    ],
  },
  {
    id: 2,
    name: "Room 2",
    coinPlayer: [
      {
        name: "Bitcoin",
        image: "btc",
        chartData: [42500.21, 42510.75, 42490.5, 42515.3],
      },
      {
        name: "Ethereum",
        image: "eth",
        chartData: [42500.22, 42510.75, 42490.5, 42515.6],
      },
    ],
    coinBanker: [
      {
        name: "Binance",
        image: "bnb",
        chartData: [42500.26, 42510.75, 42490.5, 42515.7],
      },
      {
        name: "XRP",
        image: "xrp",
        chartData: [42500.25, 42510.75, 42490.5, 42515.9],
      },
    ],
    resultPlayer: [18, 12, 15, 17, 6, 9, 11, 5, 3, 0, 4, 12, 15, 17, 7, 2],
    resultBanker: [14, 16, 13, 11, 10, 5, 12, 15, 17, 6, 9, 11, 5, 3, 0, 4],

    playersAll: 56,
    time: true,
    player: 8,
    banker: 13,
    tie: 4,
    pattern: [
      "B",
      "B",
      "T",
      "T",
      "P",
      "B",
      "B",
      "P",
      "B",
      "B",
      "B",
      "P",
      "B",
      "P",
      "B",
      "P",
      "P",
      "T",
      "B",
      "B",
      "B",
      "P",
      "B",
      "P",
      "T",
    ],
  },
]);

const displayPattern = ref([
  {
    id: 1,
    playerCrypto: [
      {
        name: "Bitcoin",
        image: "btc",
        patternData: [
          [45123.7, 45234.12, 44987.3, 45456.89],
          [45234.12, 45567.8, 45098.45, 45789.23],
          [45567.8, 45321.67, 45234.9, 45678.34],
          [45321.67, 45890.45, 45187.23, 46012.78],
          [45890.45, 46234.89, 45756.12, 46345.67],
          [46234.89, 46098.34, 45987.56, 46456.23],
          [46098.34, 46567.78, 46012.89, 46789.45],
          [46567.78, 47123.45, 46345.67, 47234.89],
          [47123.45, 46987.23, 46789.34, 47345.12],
          [46987.23, 47456.78, 46834.56, 47567.89],
          [47456.78, 47234.45, 47098.23, 47678.34],
          [47234.45, 47789.67, 47345.89, 47890.23],
          [47789.67, 48234.34, 47567.12, 48345.78],
          [48234.34, 47987.89, 47789.45, 48456.23],
          [47987.89, 48567.23, 47834.67, 48678.89],
          [48567.23, 48789.45, 48234.78, 48901.34],
          [48789.45, 48456.89, 48345.23, 48789.67],
          [48456.89, 49123.34, 48567.89, 49234.45],
          [49123.34, 49456.78, 48789.23, 49567.89],
          [49456.78, 49234.45, 49098.67, 49678.23],
          [49234.45, 49789.89, 49345.34, 49890.78],
          [49789.89, 50123.45, 49567.89, 50234.67],
          [50123.45, 49987.23, 49789.45, 50345.89],
          [49987.23, 50456.78, 49834.67, 50567.23],
          [50456.78, 50789.34, 50234.89, 50890.45],
          [50789.34, 50567.89, 50345.23, 50978.67],
          [50567.89, 51123.45, 50678.34, 51234.89],
          [51123.45, 51456.78, 50987.23, 51567.34],
          [51456.78, 51234.89, 51098.45, 51678.23],
          [51234.89, 51789.34, 51345.67, 51890.78],
        ],
      },
      {
        name: "Ethereum",
        image: "eth",
        patternData: [
          [2567.89, 2634.23, 2498.67, 2678.45],
          [2634.23, 2789.56, 2567.34, 2834.78],
          [2789.56, 2698.23, 2634.89, 2756.34],
          [2698.23, 2845.67, 2612.45, 2890.23],
          [2845.67, 2934.89, 2789.23, 2978.56],
          [2934.89, 2867.34, 2823.67, 2945.78],
          [2867.34, 3012.56, 2834.89, 3067.23],
          [3012.56, 3156.78, 2978.34, 3189.45],
          [3156.78, 3089.23, 3034.67, 3234.89],
          [3089.23, 3267.45, 3012.89, 3298.67],
          [3267.45, 3198.78, 3156.23, 3345.34],
          [3198.78, 3389.56, 3234.67, 3456.89],
          [3389.56, 3467.23, 3298.45, 3523.78],
          [3467.23, 3398.89, 3345.67, 3534.23],
          [3398.89, 3578.34, 3423.89, 3612.67],
          [3578.34, 3645.78, 3489.23, 3689.45],
          [3645.78, 3567.89, 3534.34, 3723.67],
          [3567.89, 3734.23, 3612.78, 3789.34],
          [3734.23, 3823.56, 3689.89, 3867.78],
          [3823.56, 3756.34, 3723.67, 3890.23],
          [3756.34, 3934.78, 3789.45, 3978.56],
          [3934.78, 4012.34, 3867.89, 4067.23],
          [4012.34, 3945.67, 3923.45, 4089.78],
          [3945.67, 4123.89, 3978.23, 4167.34],
          [4123.89, 4234.56, 4067.78, 4278.89],
          [4234.56, 4167.23, 4123.45, 4289.67],
          [4167.23, 4345.78, 4189.34, 4378.23],
          [4345.78, 4423.45, 4278.67, 4467.89],
          [4423.45, 4356.89, 4334.23, 4489.56],
          [4356.89, 4534.67, 4378.45, 4578.23],
        ],
      },
    ],
    bankerCrypto: [
      {
        name: "Binance",
        image: "bnb",
        patternData: [
          [567.23, 589.67, 534.89, 612.45],
          [589.67, 634.23, 567.78, 656.89],
          [634.23, 612.56, 589.34, 667.78],
          [612.56, 678.89, 598.23, 689.45],
          [678.89, 723.34, 634.67, 734.89],
          [723.34, 698.78, 678.23, 745.67],
          [698.78, 756.23, 712.45, 778.89],
          [756.23, 789.67, 734.89, 812.34],
          [789.67, 767.23, 756.78, 823.45],
          [767.23, 834.89, 778.34, 856.78],
          [834.89, 812.45, 789.67, 867.23],
          [812.45, 878.34, 823.89, 889.67],
          [878.34, 923.78, 856.23, 934.45],
          [923.78, 898.45, 878.67, 945.89],
          [898.45, 967.23, 912.34, 978.67],
          [967.23, 1012.78, 945.89, 1023.45],
          [1012.78, 989.34, 967.67, 1034.89],
          [989.34, 1056.78, 1012.23, 1067.45],
          [1056.78, 1089.23, 1034.67, 1098.89],
          [1089.23, 1067.89, 1056.34, 1123.67],
          [1067.89, 1134.45, 1089.78, 1145.23],
          [1134.45, 1167.89, 1123.34, 1178.67],
          [1167.89, 1145.23, 1134.78, 1189.45],
          [1145.23, 1212.67, 1167.34, 1223.89],
          [1212.67, 1245.23, 1189.78, 1256.45],
          [1245.23, 1223.89, 1212.34, 1267.78],
          [1223.89, 1289.45, 1245.67, 1298.23],
          [1289.45, 1323.78, 1267.89, 1334.56],
          [1323.78, 1298.23, 1289.45, 1345.89],
          [1298.23, 1367.9, 1323.34, 1378.67],
        ],
      },
      {
        name: "XRP",
        image: "xrp",
        patternData: [
          [0.567, 0.623, 0.534, 0.678],
          [0.623, 0.689, 0.567, 0.723],
          [0.689, 0.645, 0.623, 0.734],
          [0.645, 0.756, 0.634, 0.789],
          [0.756, 0.823, 0.689, 0.845],
          [0.823, 0.789, 0.756, 0.867],
          [0.789, 0.878, 0.812, 0.923],
          [0.878, 0.934, 0.845, 0.967],
          [0.934, 0.898, 0.878, 0.978],
          [0.898, 0.989, 0.923, 1.023],
          [0.989, 0.956, 0.934, 1.045],
          [0.956, 1.067, 0.978, 1.089],
          [1.067, 1.123, 1.023, 1.145],
          [1.123, 1.089, 1.067, 1.167],
          [1.089, 1.189, 1.123, 1.212],
          [1.189, 1.234, 1.145, 1.256],
          [1.234, 1.198, 1.189, 1.278],
          [1.198, 1.289, 1.212, 1.323],
          [1.289, 1.345, 1.256, 1.367],
          [1.345, 1.312, 1.289, 1.389],
          [1.312, 1.423, 1.323, 1.445],
          [1.423, 1.467, 1.367, 1.489],
          [1.467, 1.434, 1.423, 1.512],
          [1.434, 1.534, 1.445, 1.556],
          [1.534, 1.578, 1.489, 1.598],
          [1.578, 1.545, 1.534, 1.623],
          [1.545, 1.645, 1.556, 1.667],
          [1.645, 1.689, 1.598, 1.712],
          [1.689, 1.656, 1.645, 1.734],
          [1.656, 1.556, 1.642, 1.712],
        ],
      },
    ],
  },
]);
export const useCryptoRooms = () => {
  // ฟังก์ชันสำหรับดึงข้อมูลห้องทั้งหมด
  const getAllRooms = () => {
    return rooms.value;
  };

  // ฟังก์ชันสำหรับดึงข้อมูลห้องตาม ID
  const getRoomById = (id: number) => {
    return rooms.value.find((room) => room.id === id);
  };

  // ฟังก์ชันสำหรับดึงข้อมูล displayPattern ตาม ID ห้อง
  const getDisplayPatternById = (id: number) => {
    return displayPattern.value.find((pattern) => pattern.id === id);
  };

  return {
    rooms,
    getAllRooms,
    getRoomById,
    getDisplayPatternById,
  };
};
