<template>
  <div class="relative">
    <div
      class="fixed right-0 top-1/2 transform -translate-y-1/2 z-8 transition-all duration-300"
      :class="isCollapsed ? 'translate-x-[calc(100%-10px)]' : ''"
    >
      <button
        @click="openRoomSelector"
        class="group flex items-center gap-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-4 py-3 rounded-l-xl shadow-lg duration-300 cursor-pointer"
      >
        <div class="flex flex-col items-end pl-1">
          <span class="text-sm font-medium"> {{ $t("change-table") }}</span>
          <span class="text-xs opacity-80">{{ $t("select-new-table") }}</span>
        </div>
        <div
          class="bg-white/20 rounded-full p-2 group-hover:bg-white/30 duration-300"
        >
          <Icon icon="lucide:layout-grid" class="w-5 h-5" />
        </div>
      </button>

      <!-- Toggle button -->
      <button
        @click="toggleCollapse"
        class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 rounded-full w-8 h-8 flex items-center justify-center shadow-lg transition-all duration-300 cursor-pointer"
        :class="{ 'rotate-180': isCollapsed }"
      >
        <Icon :icon="'lucide:chevrons-right'" class="w-5 h-5 text-white" />
      </button>
    </div>

    <!-- Room Selector Sheet -->
    <Sheet v-model:open="isRoomSelectorOpen">
      <SheetContent
        side="right"
        class="w-full overflow-y-auto bg-white dark:bg-transparent backdrop-blur-xl pb-5"
      >
        <SheetHeader>
          <SheetTitle
            class="text-2xl font-bold bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent"
          >
            เลือกห้องเล่นเกม
          </SheetTitle>
          <SheetDescription class="flex items-center">
            <RadiantText
              class="text-md transition ease-out hover:text-neutral-600 hover:duration-300 hover:dark:text-neutral-400"
              :duration="5"
            >
              เลือกห้องที่คุณต้องการเข้าร่วม</RadiantText
            >

            <span
              class="inline-flex items-center gap-1.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded-full text-sm ml-1"
            >
              <Icon icon="lucide:layout-grid" class="text-sm" />
              {{ rooms.length }} ห้อง
            </span>
          </SheetDescription>
        </SheetHeader>

        <div class="grid gap-4 px-5 -mt-5">
          <div
            v-for="room in rooms"
            :key="room.id"
            class="group relative shadow-inner bg-gradient-to-r from-gray-100/80 to-gray-200/80 dark:from-gray-800/80 dark:to-gray-900/80 overflow-hidden rounded-xl cursor-pointer transition-all duration-300 hover:shadow-inner-lg"
            @click="enterRoom(room.id)"
          >
            <!-- Room Card -->
            <div class="flex h-[180px] w-full overflow-hidden">
              <div class="h-full flex gap-2 mx-auto">
                <div
                  class="flex justify-center items-center gap-1 text-black dark:text-white text-sm"
                >
                  <div class="flex flex-col gap-2 w-[120px] overflow-hidden">
                    <!-- Player coins section -->
                    <div
                      class="bg-blue-500/10 flex items-center justify-center rounded-sm"
                    >
                      <span
                        class="text-[10px] bg-[#0066FF] text-white px-1 rounded mr-1"
                        >P</span
                      >
                      <div class="border-l border-black/5 dark:border-white/5">
                        <div
                          v-for="(coinItem, index) in room.coinPlayer"
                          :key="`player-${index}`"
                          class="flex items-center gap-1 px-2 py-[3px] rounded-sm"
                        >
                          <img
                            :src="`/images/crypto/trading/${coinItem.image}.png`"
                            :alt="coinItem.name"
                            class="w-4 h-4 object-cover"
                          />
                          <span
                            class="text-xs flex items-center justify-between w-full"
                          >
                            <div class="flex flex-col">
                              <span>{{ coinItem.name }}</span>
                              <span
                                class="text-[10px] flex items-center gap-1"
                                :class="
                                  getCoinChange(coinItem) < 0
                                    ? 'text-red-500'
                                    : 'text-green-500'
                                "
                                >${{ getCoinPrice(coinItem).toLocaleString() }}
                                <span
                                  class="text-[10px] flex items-center"
                                  :class="
                                    getCoinChange(coinItem) < 0
                                      ? 'text-red-500'
                                      : 'text-green-500'
                                  "
                                >
                                  <Icon
                                    :icon="
                                      getCoinChange(coinItem) < 0
                                        ? 'lucide:arrow-down'
                                        : 'lucide:arrow-up'
                                    "
                                    class="w-2 h-2"
                                  /> </span
                              ></span>
                            </div>
                          </span>
                        </div>
                      </div>
                    </div>

                    <!-- Banker coins section -->
                    <div
                      class="bg-red-500/10 flex items-center justify-center rounded-sm"
                    >
                      <span
                        class="text-[10px] bg-[#fa2956] text-white px-1 rounded mr-1"
                        >B</span
                      >
                      <div class="border-l border-black/5 dark:border-white/5">
                        <div
                          v-for="(coinItem, index) in room.coinBanker"
                          :key="`banker-${index}`"
                          class="flex items-center gap-1 px-2 py-[3px] rounded-sm"
                        >
                          <img
                            :src="`/images/crypto/trading/${coinItem.image}.png`"
                            :alt="coinItem.name"
                            class="w-4 h-4 object-cover"
                          />
                          <span
                            class="text-xs flex items-center justify-between w-full"
                          >
                            <div class="flex flex-col">
                              <span>{{ coinItem.name }}</span>
                              <span
                                class="text-[10px] flex items-center gap-1"
                                :class="
                                  getCoinChange(coinItem) < 0
                                    ? 'text-red-500'
                                    : 'text-green-500'
                                "
                                >${{ getCoinPrice(coinItem).toLocaleString() }}
                                <span
                                  class="text-[10px] flex items-center"
                                  :class="
                                    getCoinChange(coinItem) < 0
                                      ? 'text-red-500'
                                      : 'text-green-500'
                                  "
                                >
                                  <Icon
                                    :icon="
                                      getCoinChange(coinItem) < 0
                                        ? 'lucide:arrow-down'
                                        : 'lucide:arrow-up'
                                    "
                                    class="w-2 h-2"
                                  /> </span
                              ></span>
                            </div>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="flex flex-col justify-center items-center">
                  <!-- Rooms -->
                  <div
                    class="flex justify-center items-center dark:bg-black/50 backdrop-blur-sm px-2 rounded-sm shadow-inner mb-1 w-full"
                  >
                    <div class="flex items-center justify-between w-full">
                      <span class="text-xs font-medium flex items-center gap-1">
                        <Icon icon="lucide:home" class="w-3 h-3" />
                        ห้อง {{ room.id }}
                      </span>
                      <span
                        class="text-xs font-mono py-0.5 rounded-md text-yellow-400 flex items-center gap-0.5"
                        :class="{ 'animate-pulse': room.time < 10 }"
                      >
                        <template v-if="typeof room.time === 'boolean'">
                          <template v-if="room.time"
                            ><span>รอออกผล</span></template
                          >
                          <template v-else>
                            <Icon icon="lucide:timer" class="w-3 h-3" />{{
                              20
                            }}s</template
                          >
                        </template>
                        <template v-else> {{ room.time }}s </template>
                      </span>
                    </div>
                  </div>
                  <!-- Players Count -->

                  <div class="flex justify-between items-center w-full">
                    <div class="flex gap-1.5">
                      <!-- Banker Count -->
                      <div class="flex items-center gap-1 text-xs">
                        <span
                          class="bg-[#fa2956] bg-opacity-20 text-[#ffffff] rounded px-1"
                          >B</span
                        >{{ room.banker }}
                      </div>

                      <!-- Player Count -->
                      <div class="flex items-center gap-1 text-xs">
                        <span
                          class="bg-[#0066FF] bg-opacity-20 text-[#ffffff] rounded px-1"
                          >P</span
                        >{{ room.player }}
                      </div>

                      <!-- Tie Count -->
                      <div class="flex items-center gap-1 text-xs">
                        <span
                          class="bg-[#4CAF50] bg-opacity-20 text-[#ffffff] rounded px-1"
                          >T</span
                        >{{ room.tie }}
                      </div>
                    </div>

                    <div
                      class="flex justify-between items-center gap-1 w-auto shadow-inner dark:bg-black/50 backdrop-blur-sm px-2 py-0.5 rounded-sm"
                    >
                      <span class="relative flex h-2 w-2">
                        <span
                          class="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"
                        ></span>
                        <span
                          class="relative inline-flex rounded-full h-2 w-2 bg-red-500"
                        ></span>
                      </span>
                      <Icon icon="lucide:users" class="text-xs" />
                      <span class="text-xs font-medium flex items-center">
                        {{ room.playersAll }}
                      </span>
                    </div>
                  </div>
                  <CryptoSelectTable :data="room" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
const isCollapsed = ref(false);
const isRoomSelectorOpen = ref(false);
const { getAllRooms } = useCryptoRooms();
const rooms = computed(() => getAllRooms());
const router = useRouter();

const openRoomSelector = () => {
  isRoomSelectorOpen.value = true;
};

const enterRoom = (roomId) => {
  router.push(`/crypto/play/${roomId}`);
  isRoomSelectorOpen.value = false;
};

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// Function to get the latest price from chartData
const getCoinPrice = (coinItem) => {
  if (coinItem.chartData && coinItem.chartData.length > 0) {
    return coinItem.chartData[coinItem.chartData.length - 1];
  }
  return coinItem.price || 0;
};

// Function to calculate change based on previous value
const getCoinChange = (coinItem) => {
  if (coinItem.chartData && coinItem.chartData.length >= 2) {
    const currentPrice = coinItem.chartData[coinItem.chartData.length - 1];
    const previousPrice = coinItem.chartData[coinItem.chartData.length - 2];
    const changePercent = ((currentPrice - previousPrice) / previousPrice) * 100;
    return parseFloat(changePercent.toFixed(2));
  }
  return coinItem.change || 0;
};
</script>