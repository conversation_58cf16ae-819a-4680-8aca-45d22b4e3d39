import axios from "axios";
export default defineNuxtPlugin((nuxtApp) => {
  const isLocalhost = window.location.hostname === 'localhost';
  const axiosIns = axios.create({
    baseURL: isLocalhost 
      ? "http://127.0.0.1:4040/api"
      : "https://crypto-game/api",
  });

  const token = localStorage.getItem("token");
  if (token) {
    axiosIns.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  }
  nuxtApp.provide("axios", axiosIns);
});