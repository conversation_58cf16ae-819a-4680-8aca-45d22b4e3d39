<template>
  <div>
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <div>
            <Menubar class="border-none bg-transparent p-0">
              <MenubarMenu>
                <MenubarTrigger class="p-0">
                  <Button variant="outline" class="cursor-pointer" size="sm">
                    <Icon icon="lucide:languages" class="mr-1" />
                    <span class="text-xs font-medium">{{
                      currentLanguage
                    }}</span>
                  </Button>
                </MenubarTrigger>
                <MenubarContent align="end">
                  <MenubarItem
                    @click="changeLanguage('th')"
                    class="cursor-pointer"
                  >
                    <img
                      src="/images/flags/th.png"
                      alt="Thai Flag"
                      class="w-8 h-5 mr-2 rounded-xs"
                    />
                    ไทย
                    <Icon
                      v-if="currentLanguage === 'TH'"
                      icon="lucide:check"
                      class="ml-auto h-4 w-4"
                    />
                  </MenubarItem>
                  <MenubarSeparator />
                  <MenubarItem
                    @click="changeLanguage('en')"
                    class="cursor-pointer"
                  >
                    <img
                      src="/images/flags/en.webp"
                      alt="UK Flag"
                      class="w-8 h-5 mr-2 rounded-xs"
                    />
                    English
                    <Icon
                      v-if="currentLanguage === 'EN'"
                      icon="lucide:check"
                      class="ml-auto h-4 w-4"
                    />
                  </MenubarItem>
                </MenubarContent>
              </MenubarMenu>
            </Menubar>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{{ $t("change-language") }}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  </div>
</template>

<script setup lang="ts">
const { locale, setLocale } = useI18n();

const currentLanguage = computed(() => {
  return locale.value === "th" ? "TH" : "EN";
});

const changeLanguage = (lang) => {
  setLocale(lang);
};
</script>
