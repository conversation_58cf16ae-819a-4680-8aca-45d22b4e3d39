<template>
  <Dialog :open="open" @update:open="updateOpen">
    <DialogContent 
      class="sm:max-w-[600px] bg-gradient-to-br from-gray-900 to-gray-950 border-none shadow-2xl overflow-hidden p-0 text-white"
    >
      <div
        class="absolute inset-0 bg-grid-white/[0.02] bg-[size:20px_20px] opacity-20"
      ></div>
      <div
        class="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 opacity-30"
      ></div>

      <div class="relative p-6">
        <DialogHeader class="mb-6">
          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center"
            >
              <Icon
                icon="lucide:settings"
                class="text-white text-lg"
              />
            </div>
            <div>
              <DialogTitle
                class="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent"
              >
                ตั้งค่าชิป
              </DialogTitle>
              <DialogDescription class="text-gray-400 mt-1">
                โปรดเลือก {{ maxChips }} ชิป (เลือกได้สูงสุด {{ maxChips }} ชิป)
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div class="space-y-6">
          <!-- Predefined Chips Grid -->
          <div>
            <h3 class="text-lg font-medium text-white mb-4 flex items-center gap-2">
              <Icon icon="lucide:coins" class="text-blue-400" />
              ชิปที่มีอยู่
            </h3>
            <div class="grid grid-cols-6 gap-3">
              <div
                v-for="chip in predefinedChips"
                :key="chip.value"
                @click="toggleChipSelection(chip)"
                class="relative cursor-pointer transition-all duration-300 transform hover:scale-110"
              >
                <div
                  class="size-12 rounded-full flex items-center justify-center bg-black/30 backdrop-blur-sm shadow-lg border-2 overflow-hidden"
                  :class="[
                    isChipSelected(chip.value) 
                      ? 'scale-110 shadow-lg' 
                      : canSelectChip(chip) 
                        ? 'opacity-70 scale-100' 
                        : 'opacity-30 scale-100 cursor-not-allowed'
                  ]"
                  :style="`border-color: ${isChipSelected(chip.value) ? chip.color : canSelectChip(chip) ? chip.color : '#9CA3AF'};`"
                >
                  <div
                    v-if="isChipSelected(chip.value)"
                    class="absolute inset-0 rounded-full border-4 border-dashed animate-spin-slow"
                    :style="`border-color: ${chip.color}`"
                  ></div>
                  <span 
                    class="text-sm font-bold z-10"
                    :class="canSelectChip(chip) || isChipSelected(chip.value) ? 'text-white' : 'text-gray-400'"
                  >
                    {{ formatChipValue(chip.value) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Custom Chip Creation -->
          <div class="bg-black/30 backdrop-blur-sm rounded-xl p-5 border border-gray-800">
            <h3 class="text-lg font-medium text-white mb-4 flex items-center gap-2">
              <Icon icon="lucide:plus-circle" class="text-green-400" />
              กรอกจำนวนที่ต้องการ
            </h3>
            <div class="flex items-center gap-3 justify-center">
              <Input
                v-model="customChipValue"
                type="number"
                placeholder="กรอกจำนวน"
                class="w-32 bg-gray-800/50 border-gray-700 text-white placeholder-gray-400"
                @input="validateIntegerInput"
              />
              <span class="text-lg font-bold text-white">x100</span>
              <Icon icon="lucide:arrow-right" class="text-gray-400" />
              <div class="relative">
                <div
                  class="size-12 rounded-full flex items-center justify-center bg-black/30 backdrop-blur-sm shadow-lg border-2 overflow-hidden"
                  :style="`border-color: ${customChipValue ? '#EF4444' : '#9CA3AF'};`"
                >
                  <span class="text-sm font-bold text-white">
                    {{ customChipValue ? formatChipValue(customChipValue * 100) : '?' }}
                  </span>
                </div>
              </div>
            </div>
            <div class="flex justify-center gap-2 mt-3">
              <button
                @click="clearCustomChip"
                class="px-4 py-2 rounded-lg bg-gray-800 hover:bg-gray-700 text-gray-300 border border-gray-700 transition-all duration-300 flex items-center gap-2"
              >
                <Icon icon="lucide:x" class="text-sm" />
                ยกเลิก
              </button>
              <button
                @click="addCustomChip"
                :disabled="!canAddCustomChip"
                class="px-4 py-2 rounded-lg bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white font-medium transition-all duration-300 flex items-center gap-2 shadow-lg shadow-green-700/20 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Icon icon="lucide:check" class="text-sm" />
                ยืนยัน
              </button>
            </div>
          </div>

          <!-- Selected Chips Display -->
          <div class="bg-black/30 backdrop-blur-sm rounded-xl p-5 border border-gray-800">
            <h3 class="text-lg font-medium text-white mb-4 flex items-center gap-2">
              <Icon icon="lucide:star" class="text-yellow-400" />
              ชิปที่เลือก ({{ selectedChips.length }}/{{ maxChips }})
            </h3>
            <div class="flex items-center justify-center gap-3 min-h-[60px]">
              <div
                v-for="(chip, index) in selectedChips"
                :key="`selected-${chip.value}-${index}`"
                class="relative cursor-pointer transition-all duration-300 transform hover:scale-110"
                @click="removeSelectedChip(index)"
              >
                <div
                  class="size-12 rounded-full flex items-center justify-center bg-black/30 backdrop-blur-sm shadow-lg border-2 overflow-hidden scale-110"
                  :style="`border-color: ${chip.color};`"
                >
                  <div
                    class="absolute inset-0 rounded-full border-4 border-dashed animate-spin-slow"
                    :style="`border-color: ${chip.color}`"
                  ></div>
                  <span class="text-sm font-bold z-10 text-white">
                    {{ formatChipValue(chip.value) }}
                  </span>
                  <!-- Remove indicator on hover -->
                  <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-red-500/80 rounded-full">
                    <Icon icon="lucide:x" class="text-white text-sm" />
                  </div>
                </div>
              </div>
              
              <!-- Empty slots -->
              <div
                v-for="n in (maxChips - selectedChips.length)"
                :key="`empty-${n}`"
                class="size-12 border-2 border-dashed border-gray-600 rounded-full flex items-center justify-center bg-gray-800/30"
              >
                <Icon icon="lucide:plus" class="text-gray-400 text-lg" />
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-3">
            <button
              @click="cancel"
              class="flex-1 py-3 rounded-xl bg-gray-800 hover:bg-gray-700 text-gray-300 border border-gray-700 transition-all duration-300 flex items-center justify-center gap-2"
            >
              <Icon icon="lucide:x" class="text-sm" />
              <span>ยกเลิก</span>
            </button>

            <button
              @click="save"
              :disabled="selectedChips.length !== maxChips"
              class="flex-1 py-3 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white font-medium transition-all duration-300 flex items-center justify-center gap-2 shadow-lg shadow-blue-700/20 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Icon icon="lucide:save" class="text-sm" />
              <span>บันทึก</span>
            </button>
          </div>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  chips: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['update:open', 'update:chips', 'select-chip']);

const maxChips = 5;
const customChipValue = ref('');
const selectedChips = ref([]);

const validateIntegerInput = (event) => {
  // Remove any decimal points and non-numeric characters except integers
  const value = event.target.value.replace(/[^\d]/g, '');
  customChipValue.value = value;
};

// Predefined chips with colors
const predefinedChips = [
   { value: 5, color: '#6B7280' },
  { value: 10, color: "#10B981" },
  { value: 20, color: "#F59E0B" },
  { value: 50, color: "#8B5CF6" },
  { value: 100, color: "#EF4444" },
  { value: 200, color: '#8B5CF6' },
  { value: 500, color: '#EF4444' },
  { value: 1000, color: '#F97316' },
  { value: 2000, color: '#84CC16' },
  { value: 5000, color: '#EC4899' },
  { value: 10000, color: '#6366F1' },
  { value: 20000, color: '#14B8A6' },
  { value: 50000, color: '#F59E0B' },
];

// Helper function to ensure chips is always an array
const ensureChipsArray = (chips) => {
  if (!chips) return [];
  if (Array.isArray(chips)) return chips;
  if (typeof chips === 'object') {
    // If it's an object, try to convert it to array
    return Object.values(chips).filter(chip => chip && typeof chip === 'object');
  }
  return [];
};

// Initialize selected chips from props
watch(() => props.chips, (newChips) => {
  const chipsArray = ensureChipsArray(newChips);
  selectedChips.value = [...chipsArray];
}, { immediate: true });

// Computed properties
const canAddCustomChip = computed(() => {
  const value = parseInt(customChipValue.value);
  return value && 
         value > 0 && 
         value <= 99999 && 
         selectedChips.value.length < maxChips &&
         !isChipSelected(value * 100);
});

const formatChipValue = (value) => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    const kValue = value / 1000;
    return kValue % 1 === 0 ? `${kValue}K` : `${kValue.toFixed(1)}K`;
  }
  return value.toString();
};

const isChipSelected = (value) => {
  return selectedChips.value.some(chip => chip.value === value);
};

const canSelectChip = (chip) => {
  return selectedChips.value.length < maxChips || isChipSelected(chip.value);
};

const toggleChipSelection = (chip) => {
  if (isChipSelected(chip.value)) {
    // Remove chip
    selectedChips.value = selectedChips.value.filter(c => c.value !== chip.value);
  } else if (selectedChips.value.length < maxChips) {
    // Add chip
    selectedChips.value.push({ ...chip });
  }
};

const addCustomChip = () => {
  if (canAddCustomChip.value) {
    const value = parseInt(customChipValue.value) * 100;
    const customChip = {
      value: value,
      color: '#EF4444',
      custom: true
    };
    selectedChips.value.push(customChip);
    customChipValue.value = '';
  }
};

const clearCustomChip = () => {
  customChipValue.value = '';
};

const removeSelectedChip = (index) => {
  selectedChips.value.splice(index, 1);
};

const updateOpen = (value) => {
  emit('update:open', value);
};

const cancel = () => {
  // Reset to original chips
  const chipsArray = ensureChipsArray(props.chips);
  selectedChips.value = [...chipsArray];
  emit('update:open', false);
};

const save = () => {
  if (selectedChips.value.length === maxChips) {
    // Ensure we're emitting an array
    const chipsToEmit = [...selectedChips.value];
    console.log('Saving chips:', chipsToEmit); // Debug log
    emit('update:chips', chipsToEmit);
    emit('update:open', false);
  }
};
</script>
<style scoped>
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}


</style>