<template>
  <div>
    <NeonBorder>
      <div class="rounded-xl overflow-hidden bg-background p-3">
        <div class="flex-1 grid grid-cols-3 gap-3">
          <!-- Player Card -->
          <div class="flex flex-col gap-2 w-full">
            <div class="flex gap-2">
              <!-- ผู้เล่น 0 -->
              <button
                @click="placeBet('player', 'zero')"
                class="flex-1 bg-blue-600/10 hover:bg-blue-600/20 px-2 py-2 rounded-lg text-xs sm:text-sm transition-all duration-200 relative border border-blue-600/30"
                :class="[
                  (betConfirmed || timeoutBet) && playerBets.zero === 0
                    ? 'bg-gray-600/10 hover:bg-gray-600/20 border-gray-600/30 text-gray-500 '
                    : '',
                ]"
              >
                <p class="font-medium">{{ $t("player-zero") }}</p>
                <span class="text-xs">x2.50</span>
                <!-- Bet Badge for player zero -->
                <div
                  v-if="playerBets.zero > 0"
                  class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 dark:bg-blue-600/20 text-white rounded-full w-10 h-10 sm:w-10 sm:h-10 flex items-center justify-center shadow-lg border-2 border-white/30 dark:border-white/20 overflow-hidden backdrop-blur-lg"
                  style="box-shadow: 0 0 20px rgba(37, 99, 235, 0.6)"
                >
                  <div
                    class="absolute inset-0 rounded-full border-2 sm:border-4 border-blue-400 border-dashed animate-spin-slow"
                  ></div>
                  <div
                    class="flex items-center justify-center text-sm font-bold text-blue-600 dark:text-white"
                  >
                    {{ formatChipValue(playerBets.zero) }}
                  </div>
                </div>
              </button>

              <!-- เบิ้ล -->
              <button
                @click="placeBet('player', 'double')"
                class="flex-1 bg-blue-700/10 hover:bg-blue-700/20 px-2 py-2 rounded-lg text-xs sm:text-sm transition-all duration-200 relative border border-blue-700/30"
                :class="[
                  (betConfirmed || timeoutBet) && playerBets.double === 0
                    ? 'bg-gray-600/10 hover:bg-gray-600/20 border-gray-600/30 text-gray-500 '
                    : 'bg-blue-600/10 hover:bg-blue-600/20 border-blue-600/30',
                ]"
              >
                <p class="font-medium">{{ $t("player-double") }}</p>
                <span class="text-xs">x2.50</span>
                <!-- Bet Badge for player double -->
                <div
                  v-if="playerBets.double > 0"
                  class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 dark:bg-blue-700/20 text-white rounded-full w-10 h-10 sm:w-10 sm:h-10 flex items-center justify-center shadow-lg border-2 border-white/30 dark:border-white/20 overflow-hidden backdrop-blur-lg"
                  style="box-shadow: 0 0 20px rgba(29, 78, 216, 0.6)"
                >
                  <div
                    class="absolute inset-0 rounded-full border-2 sm:border-4 border-blue-400 border-dashed animate-spin-slow"
                  ></div>
                  <div
                    class="flex items-center justify-center text-sm font-bold text-blue-700 dark:text-white"
                  >
                    {{ formatChipValue(playerBets.double) }}
                  </div>
                </div>
              </button>
            </div>

            <div
              @click="placeBet('player', 'player')"
              class="flex flex-col items-center justify-between rounded-xl from-blue-500/10 to-blue-500/20 hover:from-blue-500/20 hover:to-blue-500/30 transition-all duration-300 p-4 sm:p-6 border border-blue-500/30 cursor-pointer relative group shadow-lg "
              :class="[
                (betConfirmed || timeoutBet) && playerBets.player === 0
                  ? ' bg-gradient-to-b from-gray-500/10 to-gray-500/20 hover:from-gray-500/20 hover:to-gray-500/30 border-gray-500/30 text-gray-500'
                  : ' bg-gradient-to-b from-blue-500/10 to-blue-500/20 hover:from-blue-500/20 hover:to-blue-500/30 border-blue-500/30 hover:shadow-blue-500/20',
              ]"
            >
              <!-- Card Header -->
              <div class="flex flex-col items-center relative z-10 py-2">
                <div
                  class="text-2xl sm:text-5xl font-bold text-blue-500 flex items-center gap-1 mb-1"
                  :class="[
                    (betConfirmed || timeoutBet) && playerBets.player === 0
                      ? 'text-gray-500'
                      : '',
                  ]"
                >
                  <span>P</span>
                </div>
                <div
                  class="text-xs sm:text-sm text-nowrap text-center font-medium text-blue-500/80 mb-1"
                  :class="[
                    (betConfirmed || timeoutBet) && playerBets.player === 0
                      ? 'text-gray-500'
                      : '',
                  ]"
                >
                  {{ $t("player") }}
                </div>
                <p
                  class="text-sm sm:text-base px-3 py-1 rounded-full"
                  :class="[
                    (betConfirmed || timeoutBet) && playerBets.player === 0
                      ? 'bg-gray-500/10'
                      : 'bg-blue-500/10',
                  ]"
                >
                  x1.98
                </p>

                <!-- Bet Badge -->
                <div
                  v-if="playerBets.player > 0"
                  class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 dark:bg-blue-500/20 text-white rounded-full w-14 h-14 sm:w-24 sm:h-24 flex items-center justify-center shadow-lg border-2 border-white/30 dark:border-white/20 overflow-hidden backdrop-blur-lg"
                  style="box-shadow: 0 0 20px rgba(59, 130, 246, 0.6)"
                >
                  <div
                    class="absolute inset-0 rounded-full border-2 sm:border-4 border-blue-400 border-dashed animate-spin-slow"
                  ></div>
                  <div
                    class="flex items-center justify-center text-xl sm:text-3xl font-bold text-blue-500 dark:text-white"
                  >
                    {{ formatChipValue(playerBets.player) }}
                  </div>
                </div>
              </div>
              <!-- Progress Bar -->
              <div
                class="h-10 sm:h-50 w-1.5 sm:w-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden absolute left-5 shadow-inner"
              >
                <div
                  class="absolute bottom-0 w-full bg-blue-500 rounded-full transition-all duration-500 shadow-lg"
                  :class="[
                    (betConfirmed || timeoutBet) && playerBets.player === 0
                      ? 'bg-gray-500'
                      : '',
                  ]"
                  :style="`height: ${playerBetPercentage}%;`"
                ></div>
              </div>
              <!-- Player Stats -->
              <div
                class="w-full mt-3 sm:mt-4 pl-5 flex items-center gap-2 sm:gap-3 relative z-10"
              >
                <!-- Stats Info -->
                <div class="flex-1 text-xs sm:text-sm">
                  <div class="flex justify-between mb-1">
                    <span
                      class="text-blue-500 font-bold text-sm sm:text-base"
                      :class="[
                        (betConfirmed || timeoutBet) && playerBets.player === 0
                          ? 'text-gray-500'
                          : '',
                      ]"
                      >{{ playerBetPercentage }}%</span
                    >
                    <span class="text-gray-500 flex items-center"
                      ><Icon icon="lucide:users" class="inline size-3 mr-1" />{{
                        playerBetCount.toLocaleString()
                      }}
                    </span>
                  </div>
                  <div
                    class="flex items-center gap-1 sm:gap-2 rounded-full px-2 sm:px-3 py-1 sm:py-1.5"
                    :class="[
                      (betConfirmed || timeoutBet) && playerBets.player === 0
                        ? 'bg-gray-500/10'
                        : 'bg-blue-500/10',
                    ]"
                  >
                    <Icon
                      icon="lucide:coins"
                      class="size-3 sm:size-4"
                      :class="[
                        (betConfirmed || timeoutBet) && playerBets.player === 0
                          ? 'text-gray-500 '
                          : 'text-yellow-500',
                      ]"
                    />
                    <span class="font-medium"
                      >฿{{ Commas(playerBetAmount) }}</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Tie Card -->
          <div class="flex flex-col gap-2 w-full">
            <div class="flex justify-center">
              <!-- คู่ 0 -->
              <button
                @click="placeBet('tie', 'zero')"
                class="px-2 py-2 w-full rounded-lg text-xs sm:text-sm transition-all duration-200 cursor-pointer relative border"
                :class="[
                  (betConfirmed || timeoutBet) && tieBets.zero === 0
                    ? 'bg-gray-600/10 hover:bg-gray-600/20 border-gray-600/30 text-gray-500'
                    : 'bg-green-600/10 hover:bg-green-600/20 border-green-600/30',
                ]"
              >
                <p class="font-medium">{{ $t("tie-zero") }}</p>
                <span class="text-xs">x10.00</span>
                <!-- Bet Badge for tie zero -->
                <div
                  v-if="tieBets.zero > 0"
                  class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 dark:bg-green-600/20 text-white rounded-full w-10 h-10 sm:w-10 sm:h-10 flex items-center justify-center shadow-lg border-2 border-white/30 dark:border-white/20 overflow-hidden backdrop-blur-lg"
                  style="box-shadow: 0 0 20px rgba(34, 197, 94, 0.6)"
                >
                  <div
                    class="absolute inset-0 rounded-full border-2 sm:border-4 border-green-400 border-dashed animate-spin-slow"
                  ></div>
                  <div
                    class="flex items-center justify-center text-sm font-bold text-green-600 dark:text-white"
                  >
                    {{ formatChipValue(tieBets.zero) }}
                  </div>
                </div>
              </button>
            </div>

            <div
              @click="placeBet('tie', 'tie')"
              class="flex flex-col items-center justify-between rounded-xl transition-all duration-300 p-4 sm:p-6 border cursor-pointer relative group shadow-lg "
              :class="[
                (betConfirmed || timeoutBet) && tieBets.tie === 0
                  ? 'bg-gradient-to-b from-gray-500/10 to-gray-500/20 hover:from-gray-500/20 hover:to-gray-500/30 border-gray-500/30 text-gray-500'
                  : 'bg-gradient-to-b from-green-500/10 to-green-500/20 hover:from-green-500/20 hover:to-green-500/30 border-green-500/30 hover:shadow-green-500/20',
              ]"
            >
              <!-- Card Header -->
              <div class="flex flex-col items-center relative z-10 py-2">
                <div
                  class="text-2xl sm:text-5xl font-bold flex items-center gap-1 mb-1"
                  :class="[
                    (betConfirmed || timeoutBet) && tieBets.tie === 0
                      ? 'text-gray-500'
                      : 'text-green-500',
                  ]"
                >
                  <span>T</span>
                </div>
                <div
                  class="text-xs sm:text-sm text-nowrap text-center font-medium mb-1"
                  :class="[
                    (betConfirmed || timeoutBet) && tieBets.tie === 0
                      ? 'text-gray-500'
                      : 'text-green-500',
                  ]"
                >
                  <p>{{ $t("tie") }}</p>
                </div>
                <p
                  class="text-sm sm:text-base px-3 py-1 rounded-full"
                  :class="[
                    (betConfirmed || timeoutBet) && tieBets.tie === 0
                      ? 'text-gray-500 bg-gray-500/10'
                      : 'bg-green-500/10',
                  ]"
                >
                  x1.98
                </p>

                <!-- Bet Badge -->
                <div
                  v-if="tieBets.tie > 0"
                  class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 dark:bg-green-500/20 text-white rounded-full w-14 h-14 sm:w-24 sm:h-24 flex items-center justify-center shadow-lg border-2 border-white/30 dark:border-white/20 overflow-hidden backdrop-blur-lg"
                  style="box-shadow: 0 0 20px rgba(34, 197, 94, 0.6)"
                >
                  <div
                    class="absolute inset-0 rounded-full border-2 sm:border-4 border-green-400 border-dashed animate-spin-slow"
                  ></div>
                  <div
                    class="flex items-center justify-center text-xl sm:text-3xl font-bold text-green-500 dark:text-white"
                  >
                    {{ formatChipValue(tieBets.tie) }}
                  </div>
                </div>
              </div>
              <!-- Progress Bar -->
              <div
                class="h-10 sm:h-50 w-1.5 sm:w-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden absolute left-5 shadow-inner"
              >
                <div
                  class="absolute bottom-0 w-full rounded-full transition-all duration-500 shadow-lg"
                  :class="[
                    (betConfirmed || timeoutBet) && tieBets.tie === 0
                      ? 'bg-gray-500'
                      : 'bg-green-500',
                  ]"
                  :style="`height: ${tieBetPercentage}%;`"
                ></div>
              </div>
              <!-- Tie Stats -->
              <div
                class="w-full mt-3 sm:mt-4 pl-5 flex items-center gap-2 sm:gap-3 relative z-10"
              >
                <!-- Stats Info -->
                <div class="flex-1 text-xs sm:text-sm">
                  <div class="flex justify-between mb-1">
                    <span
                      class="font-bold text-sm sm:text-base"
                      :class="[
                        (betConfirmed || timeoutBet) && tieBets.tie === 0
                          ? 'text-gray-500'
                          : 'text-green-500',
                      ]"
                      >{{ tieBetPercentage }}%</span
                    >
                    <span class="text-gray-500 flex items-center">
                      <Icon icon="lucide:users" class="inline size-3 mr-1" />
                      {{ tieBetCount.toLocaleString() }}
                    </span>
                  </div>
                  <div
                    class="flex items-center gap-1 sm:gap-2 0 rounded-full px-2 sm:px-3 py-1 sm:py-1.5"
                    :class="[
                      (betConfirmed || timeoutBet) && tieBets.tie === 0
                        ? 'text-gray-500 bg-gray-500/10'
                        : 'bg-green-500/10',
                    ]"
                  >
                    <Icon
                      icon="lucide:coins"
                      class="size-3 sm:size-4"
                      :class="[
                        (betConfirmed || timeoutBet) && tieBets.tie === 0
                          ? 'text-gray-500 '
                          : 'text-yellow-500',
                      ]"
                    />
                    <span class="font-medium">฿{{ Commas(tieBetAmount) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Banker Card -->
          <div class="flex flex-col gap-2 w-full">
            <div class="flex gap-2">
              <!-- เจ้ามือ 0 -->
              <button
                @click="placeBet('banker', 'zero')"
                class="flex-1 px-2 py-2 rounded-lg text-xs sm:text-sm transition-all duration-200 cursor-pointer relative border"
                :class="[
                  (betConfirmed || timeoutBet) && bankerBets.zero === 0
                    ? 'bg-gray-600/10 hover:bg-gray-600/20 border-gray-600/30 text-gray-500'
                    : 'bg-red-600/10 hover:bg-red-600/20 border-red-600/30',
                ]"
              >
                <p class="font-medium">{{ $t("banker-zero") }}</p>
                <span class="text-xs">x2.50</span>
                <!-- Bet Badge for banker zero -->
                <div
                  v-if="bankerBets.zero > 0"
                  class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 dark:bg-red-600/20 text-white rounded-full w-10 h-10 sm:w-10 sm:h-10 flex items-center justify-center shadow-lg border-2 border-white/30 dark:border-white/20 overflow-hidden backdrop-blur-lg"
                  style="box-shadow: 0 0 20px rgba(220, 38, 38, 0.6)"
                >
                  <div
                    class="absolute inset-0 rounded-full border-2 sm:border-4 border-red-400 border-dashed animate-spin-slow"
                  ></div>
                  <div
                    class="flex items-center justify-center text-sm font-bold text-red-600 dark:text-white"
                  >
                    {{ formatChipValue(bankerBets.zero) }}
                  </div>
                </div>
              </button>

              <!-- เบิ้ล -->
              <button
                @click="placeBet('banker', 'double')"
                class="flex-1 px-2 py-2 rounded-lg text-xs sm:text-sm transition-all duration-200 cursor-pointer relative border"
                :class="[
                  (betConfirmed || timeoutBet) && bankerBets.double === 0
                    ? 'bg-gray-600/10 hover:bg-gray-600/20 border-gray-600/30 text-gray-500'
                    : 'bg-red-700/10 hover:bg-red-700/20 border-red-700/30',
                ]"
              >
                <p class="font-medium">{{ $t("banker-double") }}</p>
                <span class="text-xs">x2.50</span>
                <!-- Bet Badge for banker double -->
                <div
                  v-if="bankerBets.double > 0"
                  class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 dark:bg-red-700/20 text-white rounded-full w-10 h-10 sm:w-10 sm:h-10 flex items-center justify-center shadow-lg border-2 border-white/30 dark:border-white/20 overflow-hidden backdrop-blur-lg"
                  style="box-shadow: 0 0 20px rgba(185, 28, 28, 0.6)"
                >
                  <div
                    class="absolute inset-0 rounded-full border-2 sm:border-4 border-red-400 border-dashed animate-spin-slow"
                  ></div>
                  <div
                    class="flex items-center justify-center text-sm font-bold text-red-700 dark:text-white"
                  >
                    {{ formatChipValue(bankerBets.double) }}
                  </div>
                </div>
              </button>
            </div>

            <div
              @click="placeBet('banker', 'banker')"
              class="flex flex-col items-center justify-between rounded-xl transition-all duration-300 p-4 sm:p-6 border cursor-pointer relative group shadow-lg "
              :class="[
                (betConfirmed || timeoutBet) && bankerBets.banker === 0
                  ? 'bg-gradient-to-b from-gray-500/10 to-gray-500/20 hover:from-gray-500/20 hover:to-gray-500/30 border-gray-500/30 text-gray-500'
                  : 'bg-gradient-to-b from-red-500/10 to-red-500/20 hover:from-red-500/20 hover:to-red-500/30 border-red-500/30 hover:shadow-red-500/20',
              ]"
            >
              <!-- Card Header -->
              <div class="flex flex-col items-center relative z-10 py-2">
                <div
                  class="text-2xl sm:text-5xl font-bold flex items-center gap-1 mb-1"
                  :class="[
                    (betConfirmed || timeoutBet) && bankerBets.banker === 0
                      ? 'text-gray-500'
                      : 'text-red-500',
                  ]"
                >
                  <span>B</span>
                </div>
                <div
                  class="text-xs sm:text-sm text-nowrap text-center font-medium mb-1"
                  :class="[
                    (betConfirmed || timeoutBet) && bankerBets.banker === 0
                      ? 'text-gray-500'
                      : 'text-red-500/80',
                  ]"
                >
                  <p>{{ $t("banker") }}</p>
                </div>
                <p
                  class="text-sm sm:text-base px-3 py-1 rounded-full"
                  :class="[
                    (betConfirmed || timeoutBet) && bankerBets.banker === 0
                      ? 'text-gray-500 bg-gray-500/10'
                      : 'bg-red-500/10',
                  ]"
                >
                  x1.98
                </p>

                <!-- Bet Badge -->
                <div
                  v-if="bankerBets.banker > 0"
                  class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 dark:bg-red-500/20 text-white rounded-full w-14 h-14 sm:w-24 sm:h-24 flex items-center justify-center shadow-lg border-2 border-white/30 dark:border-white/20 overflow-hidden backdrop-blur-lg"
                  style="box-shadow: 0 0 20px rgba(239, 68, 68, 0.6)"
                >
                  <div
                    class="absolute inset-0 rounded-full border-2 sm:border-4 border-red-400 border-dashed animate-spin-slow"
                  ></div>
                  <div
                    class="flex items-center justify-center text-xl sm:text-3xl font-bold text-red-500 dark:text-white"
                  >
                    {{ formatChipValue(bankerBets.banker) }}
                  </div>
                </div>
              </div>
              <!-- Progress Bar -->
              <div
                class="h-10 sm:h-50 left-5 w-1.5 sm:w-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden absolute shadow-inner"
              >
                <div
                  class="absolute bottom-0 w-full rounded-full transition-all duration-500 shadow-lg"
                  :class="[
                    (betConfirmed || timeoutBet) && bankerBets.banker === 0
                      ? 'bg-gray-500'
                      : 'bg-red-500',
                  ]"
                  :style="`height: ${bankerBetPercentage}%;`"
                ></div>
              </div>
              <!-- Banker Stats -->
              <div
                class="w-full mt-3 sm:mt-4 pl-5 flex items-center gap-2 sm:gap-3 relative z-10"
              >
                <!-- Stats Info -->
                <div class="flex-1 text-xs sm:text-sm">
                  <div class="flex justify-between mb-1">
                    <span
                      class="font-bold text-sm sm:text-base"
                      :class="[
                        (betConfirmed || timeoutBet) && bankerBets.banker === 0
                          ? 'text-gray-500'
                          : 'text-red-500',
                      ]"
                      >{{ bankerBetPercentage }}%</span
                    >
                    <span class="text-gray-500 flex items-center"
                      ><Icon icon="lucide:users" class="inline size-3 mr-1" />{{
                        bankerBetCount.toLocaleString()
                      }}
                    </span>
                  </div>
                  <div
                    class="flex items-center gap-1 sm:gap-2 rounded-full px-2 sm:px-3 py-1 sm:py-1.5"
                    :class="[
                      (betConfirmed || timeoutBet) && bankerBets.banker === 0
                        ? 'bg-gray-500/10'
                        : 'bg-red-500/10',
                    ]"
                  >
                    <Icon
                      icon="lucide:coins"
                      class="size-3 sm:size-4"
                      :class="[
                        (betConfirmed || timeoutBet) && bankerBets.banker === 0
                          ? 'text-gray-500'
                          : 'text-yellow-500',
                      ]"
                    />
                    <span class="font-medium"
                      >฿{{ Commas(bankerBetAmount) }}</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </NeonBorder>
  </div>
</template>

<script setup lang="ts">
defineProps({
  tieBets: {
    type: Object,
    required: true,
    default: () => ({
      tie: 0,
      zero: 0,
    }),
  },
  playerBets: {
    type: Object,
    default: () => ({
      player: 0,
      high: 0,
      low: 0,
      odd: 0,
      even: 0,
      zero: 0,
      double: 0,
    }),
  },

  bankerBets: {
    type: Object,
    default: () => ({
      banker: 0,
      high: 0,
      low: 0,
      odd: 0,
      even: 0,
      zero: 0,
      double: 0,
    }),
  },
  playerBetPercentage: {
    type: Number,
    default: 0,
  },
  playerBetCount: {
    type: Number,
    default: 0,
  },
  playerBetAmount: {
    type: Number,
    default: 0,
  },
  tieBetPercentage: {
    type: Number,
    default: 0,
  },
  tieBetCount: {
    type: Number,
    default: 0,
  },
  tieBetAmount: {
    type: Number,
    default: 0,
  },
  bankerBetPercentage: {
    type: Number,
    default: 0,
  },
  bankerBetCount: {
    type: Number,
    default: 0,
  },
  bankerBetAmount: {
    type: Number,
    default: 0,
  },
  betConfirmed: {
    type: Boolean,
    default: false,
  },
  timeoutBet: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["placeBet"]);

const placeBet = (type: string, option: string) => {
  emit("placeBet", type, option);
};

const Commas = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

const formatChipValue = (value) => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    const kValue = value / 1000;
    return kValue % 1 === 0 ? `${kValue}K` : `${kValue.toFixed(1)}K`;
  }
  return value.toString();
};
</script>

<style scoped>
.animate-spin-slow {
  animation: spin 8s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
