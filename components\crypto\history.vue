<template>
  <div>
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger as-child>
          <Button 
            variant="outline" 
            class="cursor-pointer"
            @click="showHistory"
            size="sm"
          >
            <Icon icon="lucide:history"  />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{{$t('history-bet')}}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  </div>
</template>

<script setup lang="ts">
const isHistoryOpen = ref(false);

const showHistory = () => {
  isHistoryOpen.value = !isHistoryOpen.value;
  console.log('Show game history');
  // Here you would implement the logic to show the history modal or navigate to history page
};

onMounted(() => {
  // Initialize history component
});
</script>