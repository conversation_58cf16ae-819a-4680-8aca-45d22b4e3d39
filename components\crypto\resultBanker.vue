<template>
  <div>
    <div
      class="w-full xl:h-[528px] overflow-clip items-center justify-center relative"
    >
      <LightSpeed
        :key="preset"
        :effect-options="selectedPreset"
        class="rounded-xl"
      />

      <div
        class="absolute inset-0 flex flex-col items-center justify-center z-5 mt-10"
      >
        <div class="flex items-center justify-center space-x-6">
          <div class="relative" :class="animationClasses.firstCoin">
            <div
              class="w-20 h-20 sm:w-24 sm:h-24 rounded-full bg-gradient-to-br from-blue-400 via-blue-500 to-purple-600 flex items-center justify-center shadow-2xl transform transition-all duration-1000 hover:scale-110"
            >
              <div
                class="w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-gradient-to-br from-blue-300 via-blue-400 to-purple-500 flex items-center justify-center border-2 border-blue-200/50 backdrop-blur-sm"
              >
                <span
                  class="text-2xl sm:text-3xl font-bold text-white drop-shadow-lg"
                >
                  {{ firstCoinDigit }}
                </span>
              </div>
            </div>
            <div
              class="absolute -top-2 -left-5 text-xs font-medium text-blue-300 bg-blue-900 px-2 py-1 rounded-full border border-blue-500/30"
            >
              <div class="flex items-center gap-1">
                <img
                  :src="`/images/crypto/trading/${data[0].image}.png`"
                  class="h-5 w-5"
                />
                <p class="uppercase">{{ data[0].image }}</p>
              </div>
            </div>
          </div>

          <div
            class="text-4xl sm:text-5xl font-bold text-blue-300/90 animate-pulse drop-shadow-lg"
            :class="animationClasses.plus"
          >
            +
          </div>

          <div class="relative" :class="animationClasses.secondCoin">
            <div
              class="w-20 h-20 sm:w-24 sm:h-24 rounded-full bg-gradient-to-br from-purple-400 via-purple-500 to-indigo-600 flex items-center justify-center shadow-2xl transform transition-all duration-1000 hover:scale-110"
            >
              <div
                class="w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-gradient-to-br from-purple-300 via-purple-400 to-indigo-500 flex items-center justify-center border-2 border-purple-200/50 backdrop-blur-sm"
              >
                <span
                  class="text-2xl sm:text-3xl font-bold text-white drop-shadow-lg"
                >
                  {{ secondCoinDigit }}
                </span>
              </div>
            </div>
            <div
              class="absolute -top-2 -left-5 text-xs font-medium text-purple-300 bg-purple-900 px-2 py-1 rounded-full border border-purple-500/30"
            >
              <div class="flex items-center gap-1">
                <img
                  :src="`/images/crypto/trading/${data[1].image}.png`"
                  class="h-5 w-5"
                />
                <p class="uppercase">{{ data[1].image }}</p>
              </div>
            </div>
          </div>
        </div>

        <div
          class="flex items-center justify-center mb-4"
          :class="animationClasses.equal"
        >
          <div
            class="text-3xl sm:text-4xl font-bold text-blue-300/90 transform transition-all duration-500 drop-shadow-lg"
          >
            =
          </div>
        </div>

        <div
          class="relative transform transition-all duration-1000"
          :class="animationClasses.result"
        >
          <div
            class="w-32 h-32 sm:w-36 sm:h-36 rounded-full bg-gradient-to-br from-indigo-500 via-purple-600 to-blue-700 flex items-center justify-center shadow-2xl relative overflow-hidden"
          >
            <!-- Circular progress indicator -->
            <div class="absolute inset-0">
              <svg class="w-full h-full" viewBox="0 0 100 100">
                <!-- Background circle -->
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="3"
                  class="text-white/20"
                />
                <!-- Progress circle -->
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="5"
                  stroke-linecap="round"
                  :stroke-dasharray="`${2 * Math.PI * 45}`"
                  :stroke-dashoffset="`${
                    2 * Math.PI * 45 * (1 - loadingProgress / 100)
                  }`"
                  transform="rotate(-90 50 50)"
                  class="text-white transition-all duration-300 ease-out"
                />
              </svg>
            </div>

            <div
              class="w-28 h-28 sm:w-32 sm:h-32 rounded-full bg-gradient-to-br from-indigo-400 via-purple-500 to-blue-600 flex items-center justify-center relative z-10 backdrop-blur-sm"
            >
              <span
                class="text-6xl font-bold text-white drop-shadow-2xl animate-pulse"
              >
                {{ calculatedResult }}
              </span>
            </div>
          </div>
        </div>

        <div
          class="text-center transform transition-all duration-1000 mt-5"
          :class="animationClasses.indicators"
        >
          <div class="flex items-center justify-center space-x-4">
            <div
              :class="[
                'px-6 py-3 rounded-full text-sm font-medium transition-all duration-500 backdrop-blur-sm border-2 flex items-center gap-2',
                isLow
                  ? 'bg-blue-500/30 border-blue-400 text-white'
                  : 'bg-red-500/30 border-red-400 text-white',
              ]"
            >
              <div
                class="w-4 h-4 rounded-full"
                :class="isLow ? 'bg-blue-400' : 'bg-red-400'"
              ></div>
              <span>{{ isLow ? "ต่ำ" : "สูง" }}</span>
            </div>
            <div
              :class="[
                'px-6 py-3 rounded-full text-sm font-medium transition-all duration-500 backdrop-blur-sm border-2 flex items-center gap-2',
                isEven
                  ? 'bg-purple-500/30 border-purple-400 text-white'
                  : 'bg-green-500/30 border-green-400 text-white',
              ]"
            >
              <div
                class="w-4 h-4 rounded-full"
                :class="isEven ? 'bg-purple-400' : 'bg-green-400'"
              ></div>
              <span>{{ isEven ? "คู่" : "คี่" }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { lightSpeedPresets } from "../components/ui/light-speed";
const preset = ref("six");
const selectedPreset = computed(() => lightSpeedPresets[preset.value]);

// เพิ่ม loading progress state
const loadingProgress = ref(0);

const props = defineProps(["data"]);

// ฟังก์ชันสำหรับดึงตัวเลขสุดท้ายจากราคา (รวมทศนิยม)
const getLastDigit = (price: number): number => {
  const priceStr = price.toString().replace(".", "");
  return parseInt(priceStr.slice(-1));
};

// คำนวณหลักหน่วยจาก chartData ตัวล่าสุด
const firstCoinDigit = computed(() => {
  console.log("props.data:", props.data);
  if (
    props.data.length > 0 &&
    props.data[0].chartData &&
    props.data[0].chartData.length > 0
  ) {
    const lastPrice =
      props.data[0].chartData[props.data[0].chartData.length - 1];
    // console.log('First coin last price:', lastPrice);
    const digit = getLastDigit(lastPrice);
    // console.log('First coin digit:', digit);
    return digit;
  }
  return 0;
});

const secondCoinDigit = computed(() => {
  if (
    props.data.length > 1 &&
    props.data[1].chartData &&
    props.data[1].chartData.length > 1
  ) {
    const lastPrice =
      props.data[1].chartData[props.data[1].chartData.length - 1];
    // console.log('Second coin last price:', lastPrice);
    const digit = getLastDigit(lastPrice);
    // console.log('Second coin digit:', digit);
    return digit;
  }
  return 0;
});

const calculatedResult = computed(() => {
  return firstCoinDigit.value + secondCoinDigit.value;
});

const isLow = computed(() => {
  return calculatedResult.value <= 13;
});

const isEven = computed(() => {
  return calculatedResult.value % 2 === 0;
});

const animationClasses = ref({
  firstCoin: "opacity-0 translate-y-10",
  secondCoin: "opacity-0 translate-y-10",
  plus: "opacity-0",
  equal: "opacity-0",
  result: "opacity-0 scale-50",
  indicators: "opacity-0 translate-y-10",
});

// ฟังก์ชันสำหรับ animate loading progress
const animateLoadingProgress = () => {
  const duration = 10000;
  const startTime = Date.now();

  const updateProgress = () => {
    const elapsed = Date.now() - startTime;
    const progress = Math.min((elapsed / duration) * 100, 100);

    // ใช้ easing function สำหรับ smooth animation
    const easeOutCubic = (t: number) => 1 - Math.pow(1 - t, 3);
    loadingProgress.value = easeOutCubic(progress / 100) * 100;

    if (progress < 100) {
      requestAnimationFrame(updateProgress);
    }
  };

  requestAnimationFrame(updateProgress);
};

onMounted(() => {
  setTimeout(() => {
    animationClasses.value.firstCoin =
      "opacity-100 translate-y-0 transition-all duration-700";
  }, 300);

  setTimeout(() => {
    animationClasses.value.plus = "opacity-100 transition-all duration-700";
  }, 600);

  setTimeout(() => {
    animationClasses.value.secondCoin =
      "opacity-100 translate-y-0 transition-all duration-700";
  }, 900);

  setTimeout(() => {
    animationClasses.value.equal = "opacity-100 transition-all duration-700";
  }, 1500);

  setTimeout(() => {
    animationClasses.value.result =
      "opacity-100 scale-100 transition-all duration-1000";
    animateLoadingProgress();
  }, 1800);

  setTimeout(() => {
    animationClasses.value.indicators =
      "opacity-100 translate-y-0 transition-all duration-700";
  }, 2300);
});
</script>

<style scoped>
@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  100% {
    transform: translateY(-10px) translateX(5px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}
</style>
