<template>
  <div>
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <div>
            <Menubar class="border-none bg-transparent p-0">
              <MenubarMenu>
                <MenubarTrigger class="p-0">
                  <Button variant="outline" class="cursor-pointer" size="sm">
                    <span
                      class="text-sm font-semibold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent"
                      >RyzeX64122420106</span
                    >
                  </Button>
                </MenubarTrigger>
                <MenubarContent align="end">
                  <MenubarItem
                    @click="editNickname"
                    class="cursor-pointer"
                  >
                    <Icon icon="lucide:edit-3" class=" text-blue-500 mr-1" />
                    <span>{{$t('edit-nickname')}}</span>
                  </MenubarItem>
                </MenubarContent>
              </MenubarMenu>
            </Menubar>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{{$t('setting-profile')}}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  </div>
</template>

<script setup lang="ts">
const editNickname = () => {
  console.log('Edit nickname clicked');
};
</script>