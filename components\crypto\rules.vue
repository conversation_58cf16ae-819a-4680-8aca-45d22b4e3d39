<template>
  <div>
    <!-- Rules Button with Tooltip -->
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger as-child>
          <Button 
            variant="outline" 
            class="cursor-pointer"
            @click="navigateToRules"
            size="sm"
          >
            <Icon icon="lucide:book-open"  />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{{$t('rules-crypto')}}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  </div>
</template>

<script setup lang="ts">
const router = useRouter();

const navigateToRules = () => {
  router.push('/crypto/crypto-rules');
};
</script>