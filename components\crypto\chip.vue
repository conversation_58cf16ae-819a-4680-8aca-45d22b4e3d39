<template>
  <div
    class="flex items-center gap-2 sm:gap-4 bg-gray-100/70 dark:bg-gray-800/80 p-2 rounded-xl backdrop-blur-md border dark:border-gray-700/50"
  >
    <!-- Chip options -->
    <div class="flex items-center gap-1 sm:gap-3">
      <div
        v-for="(chip, index) in chips"
        :key="index"
        @click="selectChip(chip.value)"
        class="relative cursor-pointer transition-all duration-300 transform hover:scale-110"
      >
        <div
          class="size-10 rounded-full flex items-center justify-center bg-white dark:bg-gray-900 shadow-lg border-2 overflow-hidden"
          :class="
            selectedChip === chip.value ? 'scale-110' : 'opacity-70 scale-100'
          "
          :style="`border-color: ${chip.color};`"
        >
          <div
            v-if="selectedChip === chip.value"
            class="absolute inset-0 rounded-full border-4 border-dashed animate-spin-slow"
            :style="`border-color: ${chip.color}`"
          ></div>
          <span class="text-sm font-bold z-10">
            {{ formatChipValue(chip.value) }}
          </span>
        </div>
      </div>
    </div>

    <!-- Action buttons -->

    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            @click="openCustomChipModal"
            class="flex items-center justify-center relative overflow-hidden transition-all duration-300 hover:bg-gradient-to-r hover:from-blue-500/20 hover:to-purple-500/20"
          >
            <Icon
              icon="lucide:circle-dollar-sign"
              class="size-3 sm:size-4 md:size-5 transition-transform duration-300 text-blue-500 dark:text-blue-400"
            />
          </Button>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <p class="text-xs">{{ $t("setting-chip") }}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
    <div class="flex items-center gap-1 sm:gap-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              @click="confirmBets"
              :disabled="isConfirmed || !hasBets"
              class="flex items-center justify-center relative overflow-hidden transition-all duration-300"
              :class="[
                isConfirmed
                  ? 'bg-gradient-to-r from-green-500/30 to-emerald-500/30 border border-green-500/50'
                  : 'hover:bg-gradient-to-r hover:from-green-500/20 hover:to-emerald-500/20',
              ]"
            >
              <Icon
                :icon="isConfirmed ? 'lucide:check-circle' : 'lucide:check'"
                class="size-3 sm:size-4 md:size-5 transition-transform duration-300"
                :class="[
                  isConfirmed
                    ? 'text-green-500 animate-pulse'
                    : 'text-green-500 dark:text-green-400',
                ]"
              />
              <span
                class="ml-1"
                :class="{ 'dark:text-green-400': isConfirmed }"
              >
                {{ $t("confirm") }}
              </span>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p class="text-xs">
              {{ $t("confirm-bet") }}
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              :disabled="isConfirmed || timeoutBet || isRepeat"
              @click="repeatBets"
              class="flex items-center justify-center relative overflow-hidden transition-all duration-300 hover:bg-gradient-to-r hover:from-amber-500/20 hover:to-yellow-500/20"
            >
              <Icon
                icon="lucide:repeat"
                class="size-3 sm:size-4 md:size-5 transition-transform duration-300 text-amber-500 dark:text-amber-400"
              />{{ $t("repeat") }}
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p class="text-xs">{{ $t("repeat-bet") }}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              :disabled=" timeoutBet"
              @click="clearAllBets"
              class="flex items-center justify-center relative overflow-hidden transition-all duration-300 hover:bg-gradient-to-r hover:from-red-500/20 hover:to-orange-500/20"
            >
              <Icon
                icon="lucide:x"
                class="size-3 sm:size-4 md:size-5 transition-transform duration-300 text-red-500 dark:text-red-400"
              />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p class="text-xs">{{ $t("clear-bet") }}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  chips: {
    type: Array,
    required: true,
  },
  selectedChip: {
    type: Number,
    required: true,
  },
  isConfirmed: {
    type: Boolean,
    default: false,
  },
  hasBets: {
    type: Boolean,
    default: false,
  },
  timeoutBet: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "update:selectedChip",
  "openCustomChipModal",
  "clearBets",
  "confirmBets",
  "repeatBets",
]);

const selectChip = (value: number) => {
  emit("update:selectedChip", value);
};

const openCustomChipModal = () => {
  emit("openCustomChipModal");
};

const clearAllBets = () => {
  emit("clearBets");
};

const confirmBets = () => {
  if (!props.isConfirmed) {
    emit("confirmBets");
  }
};
const isRepeat = ref(false);
const repeatBets = () => {
  isRepeat.value = true;
  emit("repeatBets");
};
watch(() => props.hasBets, (newValue) => {
  if (!newValue) {
    isRepeat.value = false;
  }
});

const formatChipValue = (value) => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    const kValue = value / 1000;
    return kValue % 1 === 0 ? `${kValue}K` : `${kValue.toFixed(1)}K`;
  }
  return value.toString();
};
</script>

<style scoped>
.animate-spin-slow {
  animation: spin 8s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
