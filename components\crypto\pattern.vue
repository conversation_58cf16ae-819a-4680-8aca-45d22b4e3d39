<script setup lang="ts">
const props = defineProps(["data"]);
// const props = {
//   data: {
//     data: {
//       pattern: [
//         "B",
//         "P",
//         "T",
//         "B",
//         "P",
//         "T",
//         "B",
//         "P",
//         "T",
//         "B",
//         "P",
//         "T",
//         "B",
//         "P",
//         "T",
//         "B",
//         "P",
//         "T",
//         "B",
//         "P",
//         "T",
//         "B",
//         "P",
//         "T",
//       ],
//     },
//   },
// };

const counts = computed(() => {
  if (!props.data?.pattern) return { B: 0, P: 0, T: 0, total: 0 };

  const result = { B: 0, P: 0, T: 0, total: 0 };

  props.data?.pattern.forEach((item) => {
    if (item === "B" || item === "P" || item === "T") {
      result[item]++;
      result.total++;
    }
  });

  return result;
});
</script>

<template>
  <div
    class="relative w-[250px] overflow-hidden bg-gray-100/70 dark:bg-gray-800/80 rounded-xl backdrop-blur-md border border-white/30 dark:border-gray-700/50 shadow-lg p-2"
  >
    <!-- Pattern Grid Header - Enhanced Design -->
    <div class="mb-2 relative">
      <!-- Background gradient -->
      <div
        class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg opacity-70"
      ></div>

      <!-- Header content with improved styling -->
      <div
        class="relative h-8 flex items-center justify-between px-3 rounded-lg border border-blue-500/30 dark:border-blue-400/20 backdrop-blur-sm"
      >
        <!-- Left side with icon -->
        <div class="flex items-center gap-1.5">
          <div
            class="w-4 h-4 rounded-full bg-blue-500/30 flex items-center justify-center"
          >
            <Icon icon="lucide:activity" class="text-blue-400 text-[10px]" />
          </div>
          <span
            class="text-xs font-medium bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"
          >
           {{ $t('room') }} Crypto 1
          </span>
        </div>

        <div class="flex items-center">
          <span class="text-xs text-gray-400 dark:text-gray-300">{{ $t('round') }}</span>
          <span
            class="ml-1 text-xs font-bold bg-blue-900/50 text-blue-300 px-1.5 py-0.5 rounded-md border border-blue-500/30"
          >
            24
          </span>
        </div>

        <div class="absolute right-2 top-1 flex items-center">
          <span class="relative flex h-2 w-2">
            <span
              class="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"
            ></span>
            <span
              class="relative inline-flex rounded-full h-2 w-2 bg-green-500"
            ></span>
          </span>
        </div>
      </div>
    </div>
    <!-- Pattern Grid -->
    <div class="grid grid-cols-12 gap-2">
      <template v-for="col in 12" :key="col">
        <div class="flex flex-col gap-2">
          <template v-for="row in 6" :key="row">
            <div class="relative group">
              <div
                v-if="
                  props.data?.pattern &&
                  props.data?.pattern[(col - 1) * 6 + (row - 1)]
                "
                class="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 blur transition-opacity duration-300"
                :class="{
                  'bg-[#fa2956]/70':
                    props.data?.pattern[(col - 1) * 6 + (row - 1)] === 'B',
                  'bg-[#0066FF]/70':
                    props.data?.pattern[(col - 1) * 6 + (row - 1)] === 'P',
                  'bg-[#4CAF50]/70':
                    props.data?.pattern[(col - 1) * 6 + (row - 1)] === 'T',
                }"
              ></div>

              <!-- Cell container -->
              <div
                class="aspect-square h-5 w-5 rounded-sm flex justify-center items-center relative backdrop-blur-sm"
                :class="[
                  props.data?.pattern &&
                  props.data?.pattern[(col - 1) * 6 + (row - 1)]
                    ? 'bg-gray-800/50 border border-gray-700/50'
                    : 'bg-white/40 border border-gray-800/30',
                ]"
              >
                <template
                  v-if="
                    props.data?.pattern &&
                    props.data?.pattern[(col - 1) * 6 + (row - 1)]
                  "
                >
                  <!-- Result indicator -->
                  <div class="absolute inset-0 rounded-lg overflow-hidden">
                    <div
                      class="absolute inset-0 opacity-20"
                      :class="{
                        'bg-[#fa2956]':
                          props.data?.pattern[(col - 1) * 6 + (row - 1)] ===
                          'B',
                        'bg-[#0066FF]':
                          props.data?.pattern[(col - 1) * 6 + (row - 1)] ===
                          'P',
                        'bg-[#4CAF50]':
                          props.data?.pattern[(col - 1) * 6 + (row - 1)] ===
                          'T',
                      }"
                    ></div>
                  </div>

                  <!-- Result circle -->
                  <div
                    class="w-5 h-5 text-[12px] rounded-sm flex justify-center items-center text-white font-bold shadow-lg transform transition-transform duration-300 group-hover:scale-110"
                    :class="{
                      'bg-gradient-to-br from-[#fa2956] to-[#fa2956]/80':
                        props.data?.pattern[(col - 1) * 6 + (row - 1)] ===
                        'B',
                      'bg-gradient-to-br from-[#0066FF] to-[#0066FF]/80':
                        props.data?.pattern[(col - 1) * 6 + (row - 1)] ===
                        'P',
                      'bg-gradient-to-br from-[#4CAF50] to-[#4CAF50]/80':
                        props.data?.pattern[(col - 1) * 6 + (row - 1)] ===
                        'T',
                    }"
                  >
                    {{ props.data?.pattern[(col - 1) * 6 + (row - 1)] }}
                  </div>
                </template>
              </div>
            </div>
          </template>
        </div>
      </template>
    </div>

    <div
      class="flex items-center justify-center space-x-2 rounded-lg py-1 text-xs font-medium shadow-inner mt-2"
    >
      <!-- Banker Count -->
      <div class="flex items-center space-x-1">
        <span class="text-[#fa2956]">B</span>
        <span
          class="bg-[#fa2956] bg-opacity-20 text-[#ffffff] rounded px-1.5 py-0.5"
          >{{ counts.B }}</span
        >
      </div>

      <!-- Player Count -->
      <div class="flex items-center space-x-1">
        <span class="text-[#0066FF]">P</span>
        <span
          class="bg-[#0066FF] bg-opacity-20 text-[#ffffff] rounded px-1.5 py-0.5"
          >{{ counts.P }}</span
        >
      </div>

      <!-- Tie Count -->
      <div class="flex items-center space-x-1">
        <span class="text-[#4CAF50]">T</span>
        <span
          class="bg-[#4CAF50] bg-opacity-20 text-[#ffffff] rounded px-1.5 py-0.5"
          >{{ counts.T }}</span
        >
      </div>

      <!-- Total Count -->
      <div class="border-l border-gray-700 pl-2 flex items-center space-x-1">
        <span class="text-gray-400 uppercase">{{ $t('total') }}</span>
        <span
          class="bg-gray-700 bg-opacity-50 text-white rounded px-1.5 py-0.5"
          >{{ counts.total }}</span
        >
      </div>
    </div>
  </div>
</template>
