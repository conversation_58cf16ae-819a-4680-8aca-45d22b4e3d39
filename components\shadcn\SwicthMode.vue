<template>
  <div>
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <Button variant="outline" class="cursor-pointer" size="sm">
          <Icon
            icon="radix-icons:moon"
            :class="[
              'h-[1.2rem] w-[1.2rem] transition-all',
              colorMode.preference !== 'dark'
                ? 'rotate-90 scale-0'
                : 'rotate-0 scale-100',
            ]"
          />
          <Icon
            icon="radix-icons:sun"
            :class="[
              'absolute h-[1.2rem] w-[1.2rem] transition-all',
              colorMode.preference !== 'light'
                ? 'rotate-90 scale-0'
                : 'rotate-0 scale-100',
            ]"
          />
          <Icon
            icon="radix-icons:laptop"
            :class="[
              'absolute h-[1.2rem] w-[1.2rem] transition-all',
              colorMode.preference !== 'system'
                ? 'rotate-90 scale-0'
                : 'rotate-0 scale-100',
            ]"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          @click="colorMode.preference = 'light'"
          class="cursor-pointer"
        >
          {{ $t("light-mode") }}
          <Icon
            v-if="colorMode.preference === 'light'"
            icon="lucide:check"
            class="ml-auto h-4 w-4"
          />
        </DropdownMenuItem>
        <DropdownMenuItem
          @click="colorMode.preference = 'dark'"
          class="cursor-pointer"
        >
          {{ $t("dark-mode") }}
          <Icon
            v-if="colorMode.preference === 'dark'"
            icon="lucide:check"
            class="ml-auto h-4 w-4"
          />
        </DropdownMenuItem>
        <DropdownMenuItem
          @click="colorMode.preference = 'system'"
          class="cursor-pointer"
        >
          {{ $t("system-mode") }}
          <Icon
            v-if="colorMode.preference === 'system'"
            icon="lucide:check"
            class="ml-auto h-4 w-4"
          />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
const colorMode = useColorMode();
</script>
