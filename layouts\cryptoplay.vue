<template>
  <div class="layout-container">
    <div id="scalable-wrapper" class="scalable-wrapper">
      <div id="content" class="content border-l border-r ">
        <Navbar />
        <main>
          <slot />
        </main>
      </div>
    </div>
  </div>
</template>

<script setup>
// กำหนดขนาดมาตรฐานของ UI
const STANDARD_WIDTH = 1920;
const STANDARD_HEIGHT = 1080;

// ขนาดมาตรฐานสำหรับมือถือ (แนวตั้ง)
const MOBILE_STANDARD_WIDTH = 430;
const MOBILE_STANDARD_HEIGHT = 730;

// ขนาดมาตรฐานสำหรับแท็บเล็ต
const TABLET_STANDARD_WIDTH = 1920;
const TABLET_STANDARD_HEIGHT = 1080;

const isMobile = ref(false);
const isTablet = ref(false);

// ตรวจสอบประเภทอุปกรณ์
const checkDeviceType = () => {
  const width = window.innerWidth;
  const height = window.innerHeight;

  // ตรวจสอบว่าเป็นมือถือหรือไม่ (ความกว้างน้อยกว่า 768px)
  isMobile.value = width < 768;

  // ตรวจสอบว่าเป็นแท็บเล็ตหรือไม่ (ความกว้างระหว่าง 768px และ 1024px)
  isTablet.value = width >= 768 && width <= 1024;
};

// ฟังก์ชันสำหรับปรับขนาด
const resizeHandler = () => {
  const wrapper = document.getElementById("scalable-wrapper");
  const content = document.getElementById("content");

  if (!wrapper || !content) return;

  // ตรวจสอบประเภทอุปกรณ์ก่อนปรับขนาด
  checkDeviceType();

  // เลือกขนาดมาตรฐานตามประเภทอุปกรณ์
  let standardWidth, standardHeight;

  if (isMobile.value) {
    standardWidth = MOBILE_STANDARD_WIDTH;
    standardHeight = MOBILE_STANDARD_HEIGHT;
  } else if (isTablet.value) {
    standardWidth = TABLET_STANDARD_WIDTH;
    standardHeight = TABLET_STANDARD_HEIGHT;
  } else {
    standardWidth = STANDARD_WIDTH;
    standardHeight = STANDARD_HEIGHT;
  }

  // ปรับขนาด content ตามประเภทอุปกรณ์
  content.style.width = `${standardWidth}px`;
  content.style.height = `${standardHeight}px`;

  // คำนวณอัตราส่วนการปรับขนาด
  const wrapperWidth = wrapper.clientWidth;
  const wrapperHeight = wrapper.clientHeight;
  const widthScale = wrapperWidth / standardWidth;
  const heightScale = wrapperHeight / standardHeight;

  // เลือกสเกลที่เล็กกว่าเพื่อให้เนื้อหาพอดีกับ wrapper
  const scale = Math.min(widthScale, heightScale);

  // คำนวณตำแหน่งกึ่งกลางตามแนวนอน แต่ชิดบนสุด
  const left = (wrapperWidth - standardWidth * scale) / 2;

  // ปรับขนาดและตำแหน่งของเนื้อหา
  content.style.transform = `scale(${scale})`;
  content.style.left = `${left}px`;
  content.style.top = "0px"; // ชิดบนสุด
};

onMounted(() => {
  // เรียกใช้ฟังก์ชันปรับขนาดเมื่อโหลดและเมื่อมีการเปลี่ยนขนาดหน้าจอ
  resizeHandler();
  window.addEventListener("resize", resizeHandler);

  // ตรวจจับการเปลี่ยนแปลงการหมุนหน้าจอบนมือถือ
  window.addEventListener("orientationchange", resizeHandler);
});

onBeforeUnmount(() => {
  // ลบ event listener เมื่อ component ถูกทำลาย
  window.removeEventListener("resize", resizeHandler);
  window.removeEventListener("orientationchange", resizeHandler);
});
</script>

<style scope>
.layout-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.scalable-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.content {
  position: absolute;
  overflow: hidden;
  transition: width 0.3s, height 0.3s;
  transform-origin: top left;
}
</style>
