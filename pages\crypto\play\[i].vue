<template>
  <div class="w-full pt-[80px] sm:pt-[70px]">
    <Loading v-if="loading" />
    <CryptoTable />
    <CryptoBetWallet
      :balance="balance"
      :playerBets="playerBets"
      :bankerBets="bankerBets"
      :tieBets="tieBets"
      :loading="loading"
      class="hidden sm:block"
    />
    <CryptoTime
      class="fixed right-5 lg:top-25 lg:right-20 hidden sm:block"
      :initialSeconds="20"
      :autoStart="true"
      @timeUpdate="handleTimeUpdate"
      @complete="handleTimerComplete"
      ref="timerRef"
    />

    <div class="mx-auto rounded-xl px-1">
      <CryptoDisplay :timeoutBet="timeoutBet" :data="currentRoom" :pattern="patternID"/>
      <CryptoLastPlayer :data="currentRoom" />
      <CryptoLastBanker :data="currentRoom" />
      <!-- Game Results Display -->
      <div class="fixed top-80 right-5 hidden sm:block w-[270px]">
        <div class="flex justify-center items-center">
          <div
            class="shadow-lg bg-gradient-to-r from-blue-500/20 to-red-500/20 dark:from-blue-500/10 dark:to-red-500/10 rounded-xl p-2 sm:p-4 backdrop-blur-sm"
          >
            <div class="flex items-center justify-center gap-4 sm:gap-8">
              <div class="flex flex-col items-center">
                <div class="text-sm text-blue-500 font-medium">
                  {{ $t("player") }}
                </div>
                <div class="text-3xl sm:text-5xl font-bold text-blue-500 mt-1">
                  10
                </div>
              </div>

              <div class="flex flex-col items-center">
                <div class="text-sm text-gray-500 font-medium">VS</div>
                <div
                  v-if="playerResult !== null && bankerResult !== null"
                  class="text-xl sm:text-3xl font-bold mt-1"
                  :class="
                    playerResult > bankerResult
                      ? 'text-blue-500'
                      : playerResult < bankerResult
                      ? 'text-red-500'
                      : 'text-green-500'
                  "
                >
                  {{
                    playerResult > bankerResult
                      ? "PLAYER WIN"
                      : playerResult < bankerResult
                      ? "BANKER WIN"
                      : "TIE"
                  }}
                </div>
                <div v-else class="text-lg sm:text-3xl mt-1 animate-pulse">
                  {{ $t("waiting") }}
                </div>
              </div>

              <div class="flex flex-col items-center">
                <div class="text-sm text-red-500 font-medium">
                  {{ $t("banker") }}
                </div>
                <div class="text-3xl sm:text-5xl font-bold text-red-500 mt-1">
                  5
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Chips Selection -->
    <div
      class="fixed bottom-3 left-0 right-0 z-5 p-2 flex justify-center items-end gap-3"
    >
      <CryptoPattern class="hidden lg:block w-[300px]" :data="currentRoom" />
      <div class="flex flex-col w-full sm:w-auto">
        <div>
          <div class="flex items-end gap-1">
            <!-- Player Side (Left) -->
            <CryptoBetPlayer
              :bets="playerBets"
              :betConfirmed="betConfirmed"
              :timeoutBet="timeoutBet"
              @place-bet="placeBet"
              class="sm:w-[300px] hidden sm:block"
            />

            <!-- Center/Result Area -->
            <div class="hidden sm:flex flex-col items-center">
              <CryptoBetBPT
                :tieBets="tieBets"
                :playerBets="playerBets"
                :bankerBets="bankerBets"
                :betConfirmed="betConfirmed"
                :playerBetPercentage="playerBetPercentage"
                :tieBetPercentage="tieBetPercentage"
                :bankerBetPercentage="bankerBetPercentage"
                :playerBetCount="playerBetCount"
                :tieBetCount="tieBetCount"
                :bankerBetAmount="bankerBetAmount"
                :playerBetAmount="playerBetAmount"
                :tieBetAmount="tieBetAmount"
                :bankerBetCount="bankerBetCount"
                @placeBet="placeBet"
                :timeoutBet="timeoutBet"
                class="sm:w-[700px]"
              />

              <CryptoChip
                :chips="chips"
                :hasBets="hasBets"
                :isConfirmed="betConfirmed"
                @confirmBets="handleConfirmBets"
                v-model:selectedChip="selectedChip"
                @open-custom-chip-modal="showCustomChipModal = true"
                @clear-bets="clearBets"
                @repeatBets="handleRepeatBets"
                :timeoutBet="timeoutBet"
                class="flex justify-center max-w-[800px]"
              />
            </div>

            <!-- Banker Side (Right) -->
            <div class="col-span-1">
              <CryptoBetBanker
                :bets="bankerBets"
                :betConfirmed="betConfirmed"
                :timeoutBet="timeoutBet"
                @place-bet="placeBet"
                class="sm:w-[300px] hidden sm:block"
              />
            </div>
          </div>

          <!-- Custom Chip Modal -->
          <CryptoChipcustom
            v-model:open="showCustomChipModal"
            :chips="chips"
            @update:chips="updateChips"
            @select-chip="selectQuickChip"
          />
        </div>
      </div>
      <CryptoPattern2 class="hidden lg:block w-[300px]" :data="currentRoom" />
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: "cryptoplay",
});
const { balance } = useBalance();
const betConfirmed = ref(false);
const timeoutBet = ref(false);
const loading = ref(false);
const playerResult = ref(null);
const bankerResult = ref(null);
const selectedChip = ref(10);
const timerRef = ref(null);
const showCustomChipModal = ref(false);
const { getRoomById ,getDisplayPatternById } = useCryptoRooms();
const route = useRoute();
const roomId = computed(() => parseInt(route.params.i as string));
const currentRoom = computed(() => getRoomById(roomId.value));
const patternID = computed(() => getDisplayPatternById(roomId.value));
const playerBets = ref({
  player: 0,
  high: 0,
  low: 0,
  odd: 0,
  even: 0,
  zero: 0,
  double: 0,
});

const bankerBets = ref({
  banker: 0,
  high: 0,
  low: 0,
  odd: 0,
  even: 0,
  zero: 0,
  double: 0,
});

const tieBets = ref({
  tie: 0,
  zero: 0,
});
const playerBetAmount = ref(754700);
const tieBetAmount = ref(754700);
const bankerBetAmount = ref(1500000);
const playerBetCount = ref(1818);
const tieBetCount = ref(1818);
const bankerBetCount = ref(3636);

// Total bet count
const totalBetAmount = computed(() => {
  return playerBetAmount.value + tieBetAmount.value + bankerBetAmount.value;
});

// Percentages
const playerBetPercentage = computed(() => {
  return Math.round((playerBetAmount.value / totalBetAmount.value) * 100);
});

const tieBetPercentage = computed(() => {
  return Math.round((tieBetAmount.value / totalBetAmount.value) * 100);
});

const bankerBetPercentage = computed(() => {
  return Math.round((bankerBetAmount.value / totalBetAmount.value) * 100);
});

const chips = ref([
  { value: 5, color: "#6B7280" },
  { value: 10, color: "#10B981" },
  { value: 20, color: "#F59E0B" },
  { value: 50, color: "#8B5CF6" },
  { value: 100, color: "#EF4444" },
]);

const totalBets = computed(() => {
  return (
    Object.values(playerBets.value).reduce((sum, bet) => sum + bet, 0) +
    Object.values(bankerBets.value).reduce((sum, bet) => sum + bet, 0) +
    Object.values(tieBets.value).reduce((sum, bet) => sum + bet, 0)
  );
});

const placeBet = (side, type) => {
  if (betConfirmed.value || timeoutBet.value) return;
  if (playerResult.value !== null || bankerResult.value !== null) return;
  if (balance.value < selectedChip.value) {
    alert("เงินไม่เพียงพอ");
    return;
  }

  if (side === "tie") {
    tieBets.value[type] += selectedChip.value;
  } else if (side === "player") {
    playerBets.value[type] += selectedChip.value;
  } else if (side === "banker") {
    bankerBets.value[type] += selectedChip.value;
  }

  balance.value -= selectedChip.value;
};

const clearBets = () => {
  betConfirmed.value = false;
  if (playerResult.value !== null || bankerResult.value !== null) {
    return;
  }
  // Refund all bets
  balance.value += totalBets.value;

  // Reset bets
  Object.keys(playerBets.value).forEach((key) => {
    playerBets.value[key] = 0;
  });

  Object.keys(bankerBets.value).forEach((key) => {
    bankerBets.value[key] = 0;
  });

  Object.keys(tieBets.value).forEach((key) => {
    tieBets.value[key] = 0;
  });
};

const updateChips = (newChips) => {
  chips.value = newChips.map((chip, index) => {
    return {
      value: chip.value,
      color: chip.color || chips.value[index]?.color || "#6B7280",
    };
  });
};

const selectQuickChip = (value) => {
  selectedChip.value = value;
};

const handleTimeUpdate = (seconds) => {
  if (seconds === 0) {
    if (!betConfirmed.value) {
      clearBets();
    }
    timeoutBet.value = true;
  }
};
const hasBets = computed(() => {
  // ตรวจสอบว่ามี bets หรือไม่
  const hasPlayerBets = Object.values(playerBets.value).some(
    (amount) => amount > 0
  );
  const hasBankerBets = Object.values(bankerBets.value).some(
    (amount) => amount > 0
  );
  const hasTieBets = Object.values(tieBets.value).some((amount) => amount > 0);

  return hasPlayerBets || hasBankerBets || hasTieBets;
});

const handleTimerComplete = () => {
  setTimeout(() => {
    betConfirmed.value = false;
    timeoutBet.value = false;
    clearBets();
    resetGameTimer();
  }, 10000);
};

const resetGameTimer = () => {
  if (timerRef.value) {
    timerRef.value.resetTimer();
    timerRef.value.startTimer();
  }
};
onMounted(() => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
  resetGameTimer();
  loadLastBetsFromStorage();
});

const lastBets = ref({
  player: {},
  banker: {},
  tie: {},
});

const hasLastBets = computed(() => {
  const hasPlayerLastBets = Object.values(lastBets.value.player).some(
    (amount) => amount > 0
  );
  const hasBankerLastBets = Object.values(lastBets.value.banker).some(
    (amount) => amount > 0
  );
  const hasTieLastBets = Object.values(lastBets.value.tie).some(
    (amount) => amount > 0
  );

  return hasPlayerLastBets || hasBankerLastBets || hasTieLastBets;
});

const saveCurrentBetsToStorage = () => {
  const currentBets = {
    player: { ...playerBets.value },
    banker: { ...bankerBets.value },
    tie: { ...tieBets.value },
  };

  localStorage.setItem(
    `cryptolotto_last_bets_room_${roomId.value}`,
    JSON.stringify(currentBets)
  );
  lastBets.value = currentBets;
};

const loadLastBetsFromStorage = () => {
  try {
    const stored = localStorage.getItem(
      `cryptolotto_last_bets_room_${roomId.value}`
    );
    if (stored) {
      const parsedBets = JSON.parse(stored);
      lastBets.value = parsedBets;
    }
  } catch (error) {
    console.error("Error loading last bets from storage:", error);
  }
};

const repeatLastBets = () => {
  balance.value += totalBets.value;

  // Reset bets
  Object.keys(playerBets.value).forEach((key) => {
    playerBets.value[key] = 0;
  });

  Object.keys(bankerBets.value).forEach((key) => {
    bankerBets.value[key] = 0;
  });

  Object.keys(tieBets.value).forEach((key) => {
    tieBets.value[key] = 0;
  });

  if (!hasLastBets.value || betConfirmed.value || timeoutBet.value) return;
  if (playerResult.value !== null || bankerResult.value !== null) return;

  // คำนวณจำนวนเงินที่ต้องใช้ทั้งหมด
  const totalLastBetAmount =
    Object.values(lastBets.value.player).reduce((sum, bet) => sum + bet, 0) +
    Object.values(lastBets.value.banker).reduce((sum, bet) => sum + bet, 0) +
    Object.values(lastBets.value.tie).reduce((sum, bet) => sum + bet, 0);

  // ตรวจสอบว่าเงินเพียงพอหรือไม่
  if (balance.value < totalLastBetAmount) {
    alert("เงินไม่เพียงพอสำหรับการเดิมพันซ้ำ");
    return;
  }

  // คัดลอกการเดิมพันรอบก่อนหน้า
  Object.keys(lastBets.value.player).forEach((key) => {
    if (lastBets.value.player[key] > 0) {
      playerBets.value[key] = lastBets.value.player[key];
    }
  });

  Object.keys(lastBets.value.banker).forEach((key) => {
    if (lastBets.value.banker[key] > 0) {
      bankerBets.value[key] = lastBets.value.banker[key];
    }
  });

  Object.keys(lastBets.value.tie).forEach((key) => {
    if (lastBets.value.tie[key] > 0) {
      tieBets.value[key] = lastBets.value.tie[key];
    }
  });

  // หักเงินจากยอดคงเหลือ
  balance.value -= totalLastBetAmount;
};

const handleConfirmBets = () => {
  if (hasBets.value) {
    // บันทึกการเดิมพันปัจจุบันก่อนยืนยัน
    saveCurrentBetsToStorage();
  }
  betConfirmed.value = true;
};

const handleRepeatBets = () => {
  repeatLastBets();
};
</script>

<style scoped>
.animate-spin-slow {
  animation: spin 8s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
