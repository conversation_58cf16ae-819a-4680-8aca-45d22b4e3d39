<script setup lang="ts">
const props = defineProps(["data"]);
// const props = {
//   data: {
//     data: {
//       pattern: [
//         "B",
//         "B",
//         "B",
//         "B",
//         "P",
//         "T",
//         "T",
//         "P",
//         "T",
//         "B",
//         "P",
//         "P",
//         "P",
//         "P",
//         "P",
//         "P",
//         "P",
//         "T",
//         "B",
//         "B",
//         "B",
//         "B",
//         "B",
//         "T",
//         "T",
//       ],
//     },
//   },
// };

const showDialog = ref(false);
const currentValue = ref(10);

const counts = computed(() => {
  if (!props.data?.pattern) return { B: 0, P: 0, T: 0, total: 0 };

  const result = { B: 0, P: 0, T: 0, total: 0 };

  props.data?.pattern.forEach((item) => {
    if (item === "B" || item === "P" || item === "T") {
      result[item]++;
      result.total++;
    }
  });

  return result;
});

const handleReload = () => {
  window.location.reload();
};

const bettingOptions = [
  {
    player: { min: 10, max: 1000 },
    banker: { min: 10, max: 1000 },
    tie: { min: 10, max: 100 },
    label: "10-1,000",
    preset: "10-1,000",
  },
  {
    player: { min: 100, max: 5000 },
    banker: { min: 100, max: 5000 },
    tie: { min: 10, max: 500 },
    label: "100-5,000",
    preset: "100-5,000",
  },
  {
    player: { min: 200, max: 12500 },
    banker: { min: 200, max: 12500 },
    tie: { min: 20, max: 1250 },
    label: "200-12,500",
    preset: "200-12,500",
  },
  {
    player: { min: 500, max: 25000 },
    banker: { min: 500, max: 25000 },
    tie: { min: 50, max: 2500 },
    label: "500-25,000",
    preset: "500-25,000",
  },
  {
    player: { min: 2500, max: 200000 },
    banker: { min: 2500, max: 200000 },
    tie: { min: 250, max: 20000 },
    label: "2,500-200,000",
    preset: "2,500-200,000",
  },
];

const selectedRange = ref({
  player: {
    min: 10,
    max: 1000,
  },
  banker: {
    min: 10,
    max: 1000,
  },
  tie: {
    min: 10,
    max: 100,
  },
  preset: "10-1,000",
});

const selectBettingRange = (
  playerMin: number,
  playerMax: number,
  bankerMin: number,
  bankerMax: number,
  tieMin: number,
  tieMax: number,
  preset: string
) => {
  selectedRange.value = {
    player: {
      min: playerMin,
      max: playerMax,
    },
    banker: {
      min: bankerMin,
      max: bankerMax,
    },
    tie: {
      min: tieMin,
      max: tieMax,
    },
    preset: preset,
  };
};

const confirmBettingRange = () => {
  currentValue.value = selectedRange.value.player.min;
  showDialog.value = false;
};

const openValueDialog = () => {
  showDialog.value = true;
};

const displayRange = computed(() => {
  return selectedRange.value.preset;
});

const formattedPattern = computed(() => {
  if (!props.data?.pattern) return [];

  const pattern = props.data?.pattern;
  const grid = [];
  let currentCol = [];
  let lastResult = null;

  pattern.forEach((result) => {
    // If result changes or current column has reached 6 items, start a new column
    if (
      (result !== lastResult && lastResult !== null) ||
      currentCol.length >= 6
    ) {
      grid.push([...currentCol]);
      currentCol = [result];
    } else {
      currentCol.push(result);
    }
    lastResult = result;
  });

  if (currentCol.length > 0) {
    grid.push([...currentCol]);
  }

  return grid;
});

const scrollContainer = ref(null);

const scrollToLatest = () => {
  if (scrollContainer.value) {
    scrollContainer.value.scrollLeft = scrollContainer.value.scrollWidth;
  }
};

watch(
  () => props.data?.pattern,
  () => {
    nextTick(() => {
      scrollToLatest();
    });
  },
  { deep: true }
);

onMounted(() => {
  scrollToLatest();
});
</script>

<template>
  <div
    class="relative w-[250px] overflow-hidden bg-gray-100/70 dark:bg-gray-800/80 rounded-xl backdrop-blur-md border border-white/30 dark:border-gray-700/50 shadow-lg p-2"
  >
    <!-- Control Bar - Matching Pattern Grid Header Style -->
    <div class="mb-2 relative">
      <!-- Background gradient -->
      <div
        class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg opacity-70"
      ></div>

      <!-- Control bar content with improved styling -->
      <div
        class="relative h-8 flex items-center justify-between px-3 rounded-lg border border-blue-500/30 dark:border-blue-400/20 backdrop-blur-sm"
      >
        <!-- Value Display -->
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger as-child>
              <button
                @click="openValueDialog"
                class="flex items-center gap-1.5 group cursor-pointer"
              >
                <div
                  class="w-4 h-4 rounded-full bg-blue-500/30 flex items-center justify-center"
                >
                  <Icon
                    icon="lucide:dollar-sign"
                    class="text-blue-400 text-[10px]"
                  />
                </div>
                <span
                  class="text-xs font-medium bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent group-hover:from-blue-300 group-hover:to-purple-300 transition-all duration-300"
                >
                  {{ displayRange }}
                </span>
              </button>
            </TooltipTrigger>
            <TooltipContent>
              <p>  {{ $t('setting-bet') }}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <!-- Divider -->
        <div class="h-4 w-px bg-blue-500/30"></div>

        <!-- Reload Button -->
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger as-child>
              <button
                @click="handleReload"
                class="flex items-center gap-1.5 group cursor-pointer"
              >
                <div
                  class="w-4 h-4 rounded-full bg-blue-500/30 flex items-center justify-center"
                >
                  <Icon
                    icon="lucide:refresh-cw"
                    class="text-blue-400 text-[10px] group-hover:animate-spin transition-transform duration-300"
                  />
                </div>
                <span
                  class="text-xs font-medium bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent group-hover:from-blue-300 group-hover:to-purple-300 transition-all duration-300"
                >
                   {{ $t('reload') }}
                </span>
              </button>
            </TooltipTrigger>
            <TooltipContent>
              <p>  {{ $t('reload') }}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <!-- Divider -->
        <div class="h-4 w-px bg-blue-500/30"></div>

        <!-- Signal Strength -->
        <div class="flex items-center gap-1.5">
          <div
            class="w-4 h-4 rounded-full bg-blue-500/30 flex items-center justify-center"
          >
            <Icon icon="lucide:wifi" class="text-blue-400 text-[10px]" />
          </div>
          <span
            class="text-xs font-medium bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"
          >
            1
          </span>

          <!-- Live indicator dot -->
          <span class="relative flex h-2 w-2 ml-1">
            <span
              class="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"
            ></span>
            <span
              class="relative inline-flex rounded-full h-2 w-2 bg-green-500"
            ></span>
          </span>
        </div>
      </div>
    </div>

    <!-- Pattern Grid -->
    <div class="relative">
      <!-- Scrollable container -->
      <div
        ref="scrollContainer"
        class="overflow-auto pb-2 scrollbar-thin scrollbar-thumb-blue-500/30 scrollbar-track-gray-800/20"
      >
        <div class="inline-flex gap-2" style="min-width: 100%">
          <template
            v-for="(column, colIndex) in formattedPattern"
            :key="colIndex"
          >
            <div class="flex flex-col gap-2">
              <template v-for="(result, rowIndex) in column" :key="rowIndex">
                <!-- No need for v-if="rowIndex < 6" since we're handling that in the computed property -->
                <div class="relative group">
                  <!-- Cell container -->
                  <div
                    class="aspect-square h-5 w-5 rounded-sm flex justify-center items-center relative"
                  >
                    <!-- Result indicator -->
                    <div class="absolute inset-0 rounded-lg overflow-hidden">
                      <div
                        class="absolute inset-0 opacity-20"
                        :class="{
                          'bg-[#fa2956]': result === 'B',
                          'bg-[#0066FF]': result === 'P',
                          'bg-[#4CAF50]': result === 'T',
                        }"
                      ></div>
                    </div>

                    <!-- Result circle -->
                    <div
                      class="w-5 h-5 text-[12px] rounded-sm flex justify-center items-center text-white font-bold shadow-lg transform transition-transform duration-300 group-hover:scale-110"
                      :class="{
                        'border-[#fa2956] border-2': result === 'B',
                        'border-[#0066FF] border-2': result === 'P',
                        'border-[#4CAF50] border-2': result === 'T',
                      }"
                    ></div>
                  </div>
                </div>
              </template>

              <!-- Fill empty cells in the column -->
              <template
                v-for="emptyIndex in Math.max(0, 6 - column.length)"
                :key="`empty-${emptyIndex}`"
              >
                <div
                  class="aspect-square h-5 w-5 rounded-sm flex justify-center items-center relative backdrop-blur-sm bg-white/40 border border-gray-800/30"
                ></div>
              </template>
            </div>
          </template>

          <!-- Fill empty columns if needed (only show a few for visual balance) -->
          <template
            v-for="emptyCol in Math.min(
              3,
              Math.max(0, 12 - formattedPattern.length)
            )"
            :key="`empty-col-${emptyCol}`"
          >
            <div class="flex flex-col gap-2">
              <template v-for="emptyRow in 6" :key="`empty-cell-${emptyRow}`">
                <div
                  class="aspect-square h-5 w-5 rounded-sm flex justify-center items-center relative backdrop-blur-sm bg-white/40 border border-gray-800/30"
                ></div>
              </template>
            </div>
          </template>
        </div>
      </div>
    </div>

    <div
      class="flex items-center justify-center space-x-2 rounded-lg mt-1 text-xs font-medium shadow-inner"
    >
      <!-- Latest Results -->
      <div class="flex items-center space-x-1">
        <span class="text-gray-400 text-[10px]">  {{ $t('lastresult') }}</span>
        <div class="flex space-x-1">
          <span
            v-for="(result, index) in props.data?.pattern.slice(-1).reverse()"
            :key="index"
            class="w-5 h-5 flex items-center justify-center rounded-sm text-white font-medium"
            :class="{
              ' border-[#fa2956] border-2': result === 'B',
              ' border-[#0066FF] border-2': result === 'P',
              'border-[#4CAF50] border-2': result === 'T',
            }"
          >
          </span>
        </div>
      </div>

      <div class="border-l border-gray-700 pl-2 flex items-center space-x-1">
        <span class="text-gray-400 text-[10px]">  {{ $t('round') }}</span>
        <span
          class="bg-gray-700 bg-opacity-50 text-white rounded px-1.5 py-0.5 min-w-[20px] text-center"
          >{{ counts.total }}</span
        >
      </div>
    </div>

    <Dialog v-model:open="showDialog">
      <DialogContent
        class="max-w-[550px] bg-gradient-to-br from-gray-900 to-gray-950 border-none shadow-2xl overflow-hidden p-0 text-white"
      >
        <div
          class="absolute inset-0 bg-grid-white/[0.02] bg-[size:20px_20px] opacity-20"
        ></div>
        <div
          class="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 opacity-30"
        ></div>

        <div class="relative p-6">
          <DialogHeader class="mb-6">
            <div class="flex items-center gap-3">
              <div
                class="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center"
              >
                <Icon
                  icon="lucide:sliders-horizontal"
                  class="text-white text-lg"
                />
              </div>
              <div>
                <DialogTitle
                  class="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent"
                >
                  กำหนดวงเงินเดิมพัน
                </DialogTitle>
                <DialogDescription class="text-gray-400 mt-1">
                  เลือกช่วงวงเงินที่ต้องการเดิมพันในแต่ละครั้ง
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div class="space-y-6">
            <!-- Betting Range Options -->
            <div class="grid grid-cols-3 gap-3">
              <button
                v-for="option in bettingOptions"
                :key="option.label"
                @click="
                  selectBettingRange(
                    option.player.min,
                    option.player.max,
                    option.banker.min,
                    option.banker.max,
                    option.tie.min,
                    option.tie.max,
                    option.preset
                  )
                "
                class="relative group overflow-hidden"
              >
                <div
                  class="absolute inset-0 rounded-xl transition-all duration-300"
                  :class="[
                    option.preset === selectedRange.preset
                      ? 'bg-gradient-to-br from-blue-500 to-purple-600 opacity-100'
                      : 'bg-gray-800/50 group-hover:bg-gray-800 opacity-70',
                  ]"
                ></div>
                <div
                  class="absolute inset-0 rounded-xl transition-all duration-300"
                  :class="[
                    option.preset === selectedRange.preset
                      ? 'bg-gradient-to-br from-blue-500 to-purple-600 opacity-100'
                      : 'bg-gray-800/50 group-hover:bg-gray-800 opacity-70',
                  ]"
                ></div>
                <div
                  class="absolute inset-0 rounded-xl transition-all duration-300"
                  :class="[
                    option.player.min === selectedRange.player.min
                      ? 'bg-gradient-to-br from-blue-500 to-purple-600 opacity-100'
                      : 'bg-gray-800/50 group-hover:bg-gray-800 opacity-70',
                  ]"
                ></div>

                <div
                  class="absolute inset-0 rounded-xl border transition-all duration-300"
                  :class="[
                    option.player.min === selectedRange.player.min
                      ? 'border-blue-400/50 shadow-lg shadow-blue-500/20'
                      : 'border-gray-700/50 group-hover:border-gray-600/50',
                  ]"
                ></div>

                <div
                  class="relative h-20 flex flex-col items-center justify-center p-3"
                >
                  <span
                    class="text-sm font-medium transition-all duration-300"
                    :class="[
                      option.player.min === selectedRange.player.min
                        ? 'text-white'
                        : 'text-gray-400 group-hover:text-white',
                    ]"
                  >
                    {{ option.label }}
                  </span>

                  <div
                    class="mt-2 flex items-center gap-1"
                    :class="{ 'opacity-0': option.custom }"
                  >
                    <Icon
                      icon="lucide:coins"
                      class="text-xs transition-all duration-300"
                      :class="[
                        option.player.min === selectedRange.player.min
                          ? 'text-yellow-300'
                          : 'text-gray-500 group-hover:text-yellow-300',
                      ]"
                    />
                    <span
                      class="text-xs transition-all duration-300"
                      :class="[
                        option.player.min === selectedRange.player.min
                          ? 'text-yellow-300'
                          : 'text-gray-500 group-hover:text-yellow-300',
                      ]"
                    >
                      {{ option.player.min ? option.player.min : "" }}
                    </span>
                  </div>
                </div>
              </button>
            </div>

            <!-- Betting Limits Summary -->
            <div
              class="bg-black/30 backdrop-blur-sm rounded-xl p-5 border border-gray-800"
            >
              <h3
                class="text-lg font-medium text-white mb-4 flex items-center gap-2"
              >
                <Icon icon="lucide:info" class="text-blue-400" />
                รายละเอียดวงเงิน
              </h3>

              <div class="grid grid-cols-3 gap-4">
                <div class="space-y-1">
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 rounded-full bg-blue-500"></div>
                    <span class="text-blue-400 font-medium">PLAYER</span>
                  </div>
                  <div
                    class="bg-blue-900/20 rounded-lg px-3 py-2 border border-blue-500/20"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-xs text-gray-400">ต่ำสุด</span>
                      <span class="text-sm text-white font-medium">{{
                        selectedRange.player.min.toLocaleString()
                      }}</span>
                    </div>
                    <div class="flex items-center justify-between mt-1">
                      <span class="text-xs text-gray-400">สูงสุด</span>
                      <span class="text-sm text-white font-medium">{{
                        selectedRange.player.max.toLocaleString()
                      }}</span>
                    </div>
                  </div>
                </div>

                <div class="space-y-1">
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 rounded-full bg-red-500"></div>
                    <span class="text-red-400 font-medium">BANKER</span>
                  </div>
                  <div
                    class="bg-red-900/20 rounded-lg px-3 py-2 border border-red-500/20"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-xs text-gray-400">ต่ำสุด</span>
                      <span class="text-sm text-white font-medium">{{
                        selectedRange.banker.min.toLocaleString()
                      }}</span>
                    </div>
                    <div class="flex items-center justify-between mt-1">
                      <span class="text-xs text-gray-400">สูงสุด</span>
                      <span class="text-sm text-white font-medium">{{
                        selectedRange.banker.max.toLocaleString()
                      }}</span>
                    </div>
                  </div>
                </div>

                <div class="space-y-1">
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 rounded-full bg-green-500"></div>
                    <span class="text-green-400 font-medium">TIE</span>
                  </div>
                  <div
                    class="bg-green-900/20 rounded-lg px-3 py-2 border border-green-500/20"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-xs text-gray-400">ต่ำสุด</span>
                      <span class="text-sm text-white font-medium">{{
                        selectedRange.tie.min.toLocaleString()
                      }}</span>
                    </div>
                    <div class="flex items-center justify-between mt-1">
                      <span class="text-xs text-gray-400">สูงสุด</span>
                      <span class="text-sm text-white font-medium">{{
                        selectedRange.tie.max.toLocaleString()
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-3">
              <button
                @click="showDialog = false"
                class="flex-1 py-3 rounded-xl bg-gray-800 hover:bg-gray-700 text-gray-300 border border-gray-700 transition-all duration-300 flex items-center justify-center gap-2"
              >
                <Icon icon="lucide:x" class="text-sm" />
                <span>ยกเลิก</span>
              </button>

              <button
                @click="confirmBettingRange"
                class="flex-1 py-3 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white font-medium transition-all duration-300 flex items-center justify-center gap-2 shadow-lg shadow-blue-700/20"
              >
                <Icon icon="lucide:check" class="text-sm" />
                <span>ยืนยัน</span>
              </button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<style scoped>
.overflow-auto::-webkit-scrollbar {
  height: 1px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.2);
  border-radius: 10px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 10px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}
</style>
