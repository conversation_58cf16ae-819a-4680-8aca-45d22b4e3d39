// https://nuxt.com/docs/api/configuration/nuxt-config
import tailwindcss from "@tailwindcss/vite";
export default defineNuxtConfig({
  ssr: false,
  compatibilityDate: "2024-11-01",
  devtools: { enabled: false },
  css: ["~/assets/css/main.css"],

  vite: {
    plugins: [tailwindcss()],
  },
  colorMode: {
    preference: "system", // default value of $colorMode.preference
    fallback: "light", // fallback value if not system preference found
    hid: "nuxt-color-mode-script",
    globalName: "__NUXT_COLOR_MODE__",
    componentName: "ColorScheme",
    classPrefix: "",
    classSuffix: "",
    storage: "localStorage", // or 'sessionStorage' or 'cookie'
    storageKey: "nuxt-color-mode",
  },
  app: {
    pageTransition: { name: "page", mode: "out-in" },
  },
  modules: [
    "@nuxtjs/color-mode",
    "shadcn-nuxt",
    [
      "@nuxtjs/google-fonts",
      {
        families: {
          Kanit: [100, 300, 400, 500, 700, 900],
        },
      },
    ],
    "@nuxt/image",
    "@pinia/nuxt",
    "@nuxtjs/i18n",
  ],
  i18n: {
    bundle: {
      optimizeTranslationDirective: false 
    },
    strategy: "no_prefix",
    defaultLocale: "th",
    locales: [
      { code: "en", name: "English", file: "en.json" },
      { code: "th", name: "Thailand", file: "th.json" },
    ],
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: "i18n_redirected",
      redirectOn: "root",
      alwaysRedirect: true,
      cookieSecure: process.env.NODE_ENV === "production",
    },
  },
  shadcn: {
    prefix: "",
    /**
     * Directory that the component lives in.
     * @default "./components/ui"
     */
    componentDir: "./components/ui",
  },
  components: [
    "~/components",
    {
      path: "~/components/ui",
      extensions: ["vue"],
      prefix: "",
    },
  ],

  nitro: {
    preset: "node-server",
    // ตั้งค่า memory limit สำหรับ server
    runtimeConfig: {
      memory: {
        maxHeapSize: "512MB",
      },
    },
  },
});
