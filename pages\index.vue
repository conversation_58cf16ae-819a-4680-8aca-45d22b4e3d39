<template>
  <div class="min-h-screen dark:text-white p-6">
     <ShadcnSwicthMode class="z-[99] fixed top-5 right-5"/>
    <div
      v-if="isLoading"
      class="fixed inset-0 flex items-center justify-center z-[9999] bg-black/70"
    >
      <div class="flex flex-col items-center">
        <div
          class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-white"
        ></div>
        <p class="mt-4 text-white text-lg">กำลังโหลด...</p>
      </div>
    </div>
    <div v-if="hasScrolled">
      <ScrollIsland title="เลื่อนลง" >
        <div class="my-3 flex flex-col gap-2">
          <div @click="scrollToSection('home')"># หน้าหลัก</div>
          <div @click="scrollToSection('beam')"># ธนาคารที่รองรับ</div>
          <div @click="scrollToSection('meteorcard')"># แพลตฟอร์มล็อตเตอรี่คริปโต</div>
          <div @click="scrollToSection('review')"># รีวิวจากผู้ใช้งานจริง</div>
          <div @click="scrollToSection('robot')"># เทคโนโลยีบล็อกเชน</div>

        </div>
      </ScrollIsland>
    </div>

    <div class="container mx-auto">
      <section id="home" class="h-screen flex items-center justify-center">
        <IndexShow />
      </section>

      <section id="beam">
        <IndexBeam />
      </section>

       <section id="meteorcard">
        <IndexMeteorsCard />
      </section>

      <section id="review">
        <IndexReview />
      </section>

       <section id="robot">
        <IndexRobot />
      </section>

        <section id="sponser">
        <IndexSponser />
      </section>

      <!-- <section id="map">
        <IndexMap />
      </section> -->
    </div>
  </div>
</template>

<script setup lang="ts">
const hasScrolled = ref(false);
const isLoading = ref(false);

const checkScroll = () => {
  hasScrolled.value = window.scrollY > 50;
};

const scrollToSection = (sectionId) => {
  setTimeout(() => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "start"
      });
    } else {
      console.warn(`Element with id "${sectionId}" not found`);
    }
  }, 100);
};

onMounted(() => {
  // isLoading.value = true;
  checkScroll();
  window.addEventListener("scroll", checkScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", checkScroll);
});

</script>

<style></style>
