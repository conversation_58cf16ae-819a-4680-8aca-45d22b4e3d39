<template>
  <div>
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger as-child>
          <Button
            variant="outline"
            class="cursor-pointer"
            @click="toggleFullscreen"
            size="sm"
          >
            <Icon
              icon="lucide:maximize"
              :class="[
                'transition-all',
                isFullscreen ? 'rotate-90 scale-0' : 'rotate-0 scale-100',
              ]"
            />
            <Icon
              icon="lucide:minimize"
              :class="[
                'absolute transition-all',
                !isFullscreen ? 'rotate-90 scale-0' : 'rotate-0 scale-100',
              ]"
            />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>
            {{ isFullscreen ? $t("closefullscreen") : $t("openfullscreen") }}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  </div>
</template>

<script setup lang="ts">
const isFullscreen = ref(false);

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement
      .requestFullscreen()
      .then(() => {
        isFullscreen.value = true;
      })
      .catch((err) => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      });
  } else {
    if (document.exitFullscreen) {
      document
        .exitFullscreen()
        .then(() => {
          isFullscreen.value = false;
        })
        .catch((err) => {
          console.error(`Error attempting to exit fullscreen: ${err.message}`);
        });
    }
  }
};

onMounted(() => {
  const handleFullscreenChange = () => {
    isFullscreen.value = !!document.fullscreenElement;
  };

  document.addEventListener("fullscreenchange", handleFullscreenChange);

  onUnmounted(() => {
    document.removeEventListener("fullscreenchange", handleFullscreenChange);
  });
});
</script>
