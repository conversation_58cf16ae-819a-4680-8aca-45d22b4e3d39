<template>
  <div>
    <Drawer v-model:open="isOpen">
      <DrawerTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          class="cursor-pointer group relative overflow-hidden"
        >
          <span
            class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          ></span>
          <Icon
            icon="lucide:menu"
            class="transition-all"
            :class="[isOpen ? 'rotate-90 scale-0' : 'rotate-0 scale-100']"
          />
          <Icon
            icon="lucide:x"
            class="absolute transition-all"
            :class="[!isOpen ? 'rotate-90 scale-0' : 'rotate-0 scale-100']"
          />
        </Button>
      </DrawerTrigger>
      <DrawerContent class="backdrop-blur-xl bg-white/90 dark:bg-black/10">
        <div class="flex flex-col h-full mb-5">
          <DrawerHeader>
            <DrawerTitle
              class="text-xl font-bold bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent flex items-center justify-between"
            >
              <div class="flex items-center gap-2">
                <div class="h-8 w-8 relative">
                  <div
                    class="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse"
                  ></div>
                  <div
                    class="absolute inset-0.5 bg-white dark:bg-black rounded-full flex items-center justify-center"
                  >
                    <Icon
                      icon="mdi:currency-btc"
                      class="text-blue-600 dark:text-purple-400"
                    />
                  </div>
                </div>
                Crypto Game
              </div>
              <div class="flex gap-2 items-center">
                <CryptoRules class="text-black dark:text-white" />
                <ShadcnSwicthMode class="text-black dark:text-white" />
              </div>
            </DrawerTitle>
            <DrawerDescription class="text-center mt-2">
              <RadiantText
                class="transition ease-out hover:text-neutral-600 hover:duration-300 hover:dark:text-neutral-400"
                :duration="5"
              >
                เว็บหวยคริปโตอันดับ 1 ของไทย</RadiantText
              >
            </DrawerDescription>
          </DrawerHeader>

          <!-- Menu items with improved centering and styling -->
          <div class="flex-1 flex items-center justify-center px-4 pb-6">
            <div class="flex flex-col gap-6 w-full max-w-xs">
              <div
                v-for="(item, index) in menuItems"
                :key="index"
                class="relative group"
              >
                <div
                  v-if="route.path === item.path"
                  class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-600/10 rounded-xl blur-md"
                ></div>
                <NeonBorder
                  :animation-type="'full'"
                  v-if="route.path === item.path"
                  color1="#3b82f6"
                  color2="#9333ea"
                  class="absolute inset-0"
                />
                <NuxtLink
                  :to="item.path"
                  @click="closeDrawer"
                  class="bg-background flex items-center gap-4 p-3 rounded-lg transition-all duration-300 hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-600/10 group relative z-10 shadow-lg backdrop-blur-sm"
                  :class="
                    route.path === item.path
                      ? 'bg-gradient-to-r from-blue-500/20 to-purple-600/20 font-medium'
                      : ''
                  "
                >
                  <div
                    class="flex items-center justify-center w-14 h-14 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-600/20 group-hover:from-blue-500/40 group-hover:to-purple-600/40 transition-all duration-300 shadow-inner"
                  >
                    <Icon
                      :icon="item.icon"
                      class="h-7 w-7 transform group-hover:scale-110 transition-transform text-blue-600 dark:text-blue-400"
                    />
                  </div>
                  <div class="flex flex-col">
                    <span
                      class="text-xl font-medium text-gray-800 dark:text-white"
                      >{{ item.label }}</span
                    >
                    <span class="text-xs text-gray-500 dark:text-gray-400">{{
                      item.description
                    }}</span>
                  </div>
                  <Icon
                    icon="lucide:chevron-right"
                    class="ml-auto h-5 w-5 text-gray-400 group-hover:text-blue-500 dark:group-hover:text-purple-400 transform group-hover:translate-x-1 transition-transform"
                  />
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const isOpen = ref(false);
const currentGameId = ref(1);
const closeDrawer = () => {
  isOpen.value = false;
};

const randomRoom = computed(() => {
  return `/crypto/play/${currentGameId.value}`;
});
const menuItems = [
  {
    path: "/",
    icon: "lucide:home",
    label: "หน้าหลัก",
    description: "กลับสู่หน้าแรก",
  },
  {
    path: "/crypto/crypto-rooms",
    icon: "lucide:layout-grid",
    label: "ห้องเล่นเกม",
    description: "เลือกห้องเกมที่คุณชอบ",
  },
  {
    path: randomRoom,
    icon: "lucide:gamepad-2",
    label: "เล่นเกม",
    description: "เริ่มเล่นเกมทันที",
  },
];
</script>
