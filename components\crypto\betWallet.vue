<template>
  <div class="fixed left-0 top-80 transform -translate-y-1/2 z-8">
    <!-- Wallet Container -->
    <div
      class="flex flex-col gap-3 bg-gradient-to-r from-gray-100/80 to-gray-200/80 dark:from-gray-800/80 dark:to-gray-900/80 p-3 rounded-r-xl border-blue-500/30 backdrop-blur-sm shadow-lg"
    >
      <!-- Balance Section -->
      <div
        class="bg-gradient-to-r from-blue-500 to-purple-600 px-3 py-2 rounded-xl flex items-center gap-2 group hover:shadow-md hover:shadow-blue-500/20 transition-all duration-300"
      >
        <div class="flex flex-col">
          <span class="text-white/70 text-xs">{{ $t("balance") }}</span>
          <span
            class="text-white text-sm sm:text-base flex items-center font-medium"
          >
            <Icon icon="mdi:currency-btc" class="mr-1" />
            <NumberTicker :value="balance" class="text-white w-32" />
          </span>
        </div>
        <button
          @click="refreshBalance"
          class="bg-white/20 rounded-full p-1 hover:bg-white/30 transition-colors cursor-pointer ml-auto group-hover:animate-pulse"
          title="รีเฟรชยอดเงิน"
        >
          <Icon
            icon="mdi:refresh"
            class="text-white text-xs"
            :class="loading ? 'animate-spin' : ''"
          />
        </button>
      </div>

      <!-- Current Bets Section -->
      <div class="space-y-2">
        <div class="text-xs px-2">{{ $t("current-bet") }}</div>

        <!-- Player Bets -->
        <!-- <div
          v-if="hasPlayerBets"
          class="bg-blue-900/50 rounded-lg p-2 border border-blue-500/30"
        >
          <div class="flex items-center gap-1.5 mb-1">
            <div class="w-2 h-2 rounded-full bg-blue-500"></div>
            <span class="text-xs font-medium text-blue-400">PLAYER</span>
          </div>
          <div class="space-y-1">
            <div
              v-for="(amount, type) in playerBets"
              :key="`player-${type}`"
              class="flex justify-between items-center text-xs"
              v-show="amount > 0"
            >
              <span class="text-white/70">{{ getBetLabel(type) }}</span>
              <span class="text-blue-300 font-medium">{{ amount }}</span>
            </div>
          </div>
        </div> -->

        <!-- Banker Bets -->
        <!-- <div
          v-if="hasBankerBets"
          class="bg-red-900/50 rounded-lg p-2 border border-red-500/30"
        >
          <div class="flex items-center gap-1.5 mb-1">
            <div class="w-2 h-2 rounded-full bg-red-500"></div>
            <span class="text-xs font-medium text-red-400">BANKER</span>
          </div>
          <div class="space-y-1">
            <div
              v-for="(amount, type) in bankerBets"
              :key="`banker-${type}`"
              class="flex justify-between items-center text-xs"
              v-show="amount > 0"
            >
              <span class="text-white/70">{{ getBetLabel(type) }}</span>
              <span class="text-red-300 font-medium">{{ amount }}</span>
            </div>
          </div>
        </div> -->

        <!-- Tie Bets -->
        <!-- <div
          v-if="hasTieBets"
          class="bg-green-900/50 rounded-lg p-2 border border-green-500/30"
        >
          <div class="flex items-center gap-1.5 mb-1">
            <div class="w-2 h-2 rounded-full bg-green-500"></div>
            <span class="text-xs font-medium text-green-400">TIE</span>
          </div>
          <div class="space-y-1">
            <div
              v-for="(amount, type) in tieBets"
              :key="`tie-${type}`"
              class="flex justify-between items-center text-xs"
              v-show="amount > 0"
            >
              <span class="text-white/70">{{ getTieBetLabel(type) }}</span>
              <span class="text-green-300 font-medium">{{ amount }}</span>
            </div>
          </div>
        </div> -->

        <!-- No Bets Placeholder -->
        <!-- <div
          v-if="!hasPlayerBets && !hasBankerBets && !hasTieBets"
          class="dark:bg-gray-800/50 rounded-lg p-2 border border-gray-700/50 text-center"
        >
          <span class="text-xs dark:text-gray-400">ยังไม่มีการเดิมพัน</span>
        </div> -->
      </div>

      <!-- Total Bet Amount -->
      <div
        class="dark:bg-gradient-to-r from-gray-800 to-gray-900 rounded-lg p-2 border border-gray-700/50"
      >
        <div class="flex justify-between items-center">
          <span class="text-xs dark:text-white/70">{{ $t("total-bet") }}</span>
          <NumberTicker
            :value="totalBetAmount"
            class="dark:text-white font-medium"
          ></NumberTicker>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
const props = defineProps({
  balance: {
    type: Number,
    default: 0,
  },
  playerBets: {
    type: Object,
    default: () => ({
      high: 0,
      low: 0,
      odd: 0,
      even: 0,
    }),
  },
  bankerBets: {
    type: Object,
    default: () => ({
      high: 0,
      low: 0,
      odd: 0,
      even: 0,
    }),
  },
  tieBets: {
    type: Object,
    default: () => ({
      player: 0,
      tie: 0,
      banker: 0,
    }),
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(["refresh-balance"]);

// Computed
const hasPlayerBets = computed(() => {
  return Object.values(props.playerBets).some((amount) => amount > 0);
});

const hasBankerBets = computed(() => {
  return Object.values(props.bankerBets).some((amount) => amount > 0);
});

const hasTieBets = computed(() => {
  return Object.values(props.tieBets).some((amount) => amount > 0);
});

const totalBetAmount = computed(() => {
  let total = 0;

  // Sum player bets
  Object.values(props.playerBets).forEach((amount) => {
    total += Number(amount);
  });

  // Sum banker bets
  Object.values(props.bankerBets).forEach((amount) => {
    total += Number(amount);
  });

  // Sum tie bets
  Object.values(props.tieBets).forEach((amount) => {
    total += Number(amount);
  });

  return total;
});

// Methods
const refreshBalance = () => {
  emit("refresh-balance");
};

// const getBetLabel = (type) => {
//   const labels = {
//     high: "สูง",
//     low: "ต่ำ",
//     odd: "คี่",
//     even: "คู่",
//   };

//   return labels[type] || type;
// };

// const getTieBetLabel = (type) => {
//   const labels = {
//     player: "Player",
//     tie: "Tie",
//     banker: "Banker",
//   };

//   return labels[type] || type;
// };
</script>
