<template>
  <div>
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger as-child>
          <div>
            <Popover>
              <PopoverTrigger>
                <Button
                  variant="outline"
                  class="cursor-pointer relative"
                  size="sm"
                >
                  <Icon
                    icon="lucide:volume-2"
                    :class="[
                      'transition-all',
                      !isAllMuted ? 'rotate-90 scale-0' : 'rotate-0 scale-100',
                    ]"
                  />
                  <Icon
                    icon="lucide:volume-x"
                    class="absolute"
                    :class="[
                      'transition-all',
                      isAllMuted ? 'rotate-90 scale-0' : 'rotate-0 scale-100',
                    ]"
                  />
                </Button>
              </PopoverTrigger>
              <PopoverContent class="w-40 p-3">
                <div class="space-y-4">
                  <div
                    class="flex items-center justify-between space-x-2 w-full"
                  >
                    <Label>{{ $t("sound-game") }}</Label>
                    <Switch
                      v-model="isGameMuted"
                      :checked="!isGameMuted"
                      @update:checked="toggleGameSound"
                    />
                  </div>

                  <div class="h-px bg-border"></div>

                  <div
                    class="flex items-center justify-between space-x-2 w-full"
                  >
                    <Label>{{ $t("sound-voice") }}</Label>
                    <Switch
                      v-model="isVoiceMuted"
                      :checked="!isVoiceMuted"
                      @update:checked="toggleVoiceSound"
                    />
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{{ $t("setting-sound") }}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  </div>
</template>

<script setup lang="ts">
const isGameMuted = ref(true);
const isVoiceMuted = ref(false);

const isAllMuted = computed(() => {
  return isGameMuted.value || isVoiceMuted.value;
});

const toggleGameSound = (checked: boolean) => {
  isGameMuted.value = !checked;

  // Add your game sound control logic here
  console.log("Game sound:", isGameMuted.value ? "muted" : "unmuted");
  // Example: document.querySelectorAll('.game-audio').forEach(el => el.muted = isGameMuted.value);
};

const toggleVoiceSound = (checked: boolean) => {
  isVoiceMuted.value = !checked;

  // Add your voice sound control logic here
  console.log("Voice sound:", isVoiceMuted.value ? "muted" : "unmuted");
  // Example: document.querySelectorAll('.voice-audio').forEach(el => el.muted = isVoiceMuted.value);
};

onMounted(() => {
  // Initialize sound states from localStorage or user preferences
  // Example:
  // isGameMuted.value = localStorage.getItem('gameSoundMuted') === 'true';
  // isVoiceMuted.value = localStorage.getItem('voiceSoundMuted') === 'true';
});

// Save sound preferences when they change
watch([isGameMuted, isVoiceMuted], ([newGameMuted, newVoiceMuted]) => {
  // Example:
  // localStorage.setItem('gameSoundMuted', newGameMuted.toString());
  // localStorage.setItem('voiceSoundMuted', newVoiceMuted.toString());
});
</script>
