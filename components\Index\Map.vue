<template>
  <div class="w-full">
    <div class="mx-auto max-w-7xl text-center">
      <p class="text-xl font-bold text-black md:text-4xl dark:text-white">
        ทั่วโลก
        <span class="text-neutral-400">
          <!-- <Motion
            v-for="(word, idx) in ''.split('')"
            :key="idx"
            as="span"
            class="inline-block"
            :initial="{ x: -10, opacity: 0 }"
            :animate="{ x: 0, opacity: 1 }"
            :transition="{ duration: 0.5, delay: idx * 0.04 }"
          >
            {{ word }}
          </Motion> -->
        </span>
      </p>
      <p class="mx-auto max-w-2xl py-4 text-sm text-neutral-500 md:text-lg">
        เชื่อมต่อกับผู้เล่นทั่วโลก เข้าถึงเกมหวยคริปโตได้จากทุกที่ ทุกเวลา
        ด้วยระบบที่ปลอดภัยและเสถียร เหมาะสำหรับนักเสี่ยงโชคทุกคน
      </p>
    </div>
    <WorldMap
      :dots="dots"
      :map-color="isDark ? '#FFFFFF40' : '#********'"
      :map-bg-color="isDark ? '' : ''"
    />
  </div>
</template>

<script lang="ts" setup>
import { Motion } from "motion-v";
const isDark = computed(() => useColorMode().value == "dark");

const dots = [
  {
    start: {
      lat: 64.2008,
      lng: -149.4937,
    }, // Alaska (Fairbanks)
    end: {
      lat: 34.0522,
      lng: -118.2437,
    }, // Los Angeles
  },
  {
    start: { lat: 64.2008, lng: -149.4937 }, // Alaska (Fairbanks)
    end: { lat: -15.7975, lng: -47.8919 }, // Brazil (Brasília)
  },
  {
    start: { lat: -15.7975, lng: -47.8919 }, // Brazil (Brasília)
    end: { lat: 38.7223, lng: -9.1393 }, // Lisbon
  },
  {
    start: { lat: 51.5074, lng: -0.1278 }, // London
    end: { lat: 28.6139, lng: 77.209 }, // New Delhi
  },
  {
    start: { lat: 28.6139, lng: 77.209 }, // New Delhi
    end: { lat: 43.1332, lng: 131.9113 }, // Vladivostok
  },
  {
    start: { lat: 28.6139, lng: 77.209 }, // New Delhi
    end: { lat: -1.2921, lng: 36.8219 }, // Nairobi
  },

   {
    start: { lat: 28.6139, lng: 250.209 }, // New Delhi
    end: { lat: -80.2921, lng: 360.8219 }, // Nairobi
  },
 
];


</script>